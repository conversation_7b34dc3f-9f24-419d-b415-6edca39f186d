import { supabase } from '@/integrations/supabase/client';

export interface LiveStream {
  id: string;
  title: string;
  description?: string;
  host_profile_id: string;
  channel_id: string;
  status: 'scheduled' | 'live' | 'ended' | 'cancelled';
  scheduled_start?: string;
  actual_start?: string;
  ended_at?: string;
  participant_count: number;
  speaker_count: number;
  max_participants?: number;
  chat_enabled?: boolean;
  audience_can_request_mic?: boolean;
  is_recorded?: boolean;
  recording_url?: string;
  settings?: any;
  created_at: string;
}

export interface StreamParticipant {
  id: string;
  stream_id: string;
  profile_id: string;
  role: 'host' | 'co_host' | 'speaker' | 'listener';
  joined_at: string;
  left_at?: string;
  mic_requested_at?: string;
  mic_granted_at?: string;
  is_muted: boolean;
}

export interface StreamChatMessage {
  id: string;
  stream_id: string;
  profile_id: string;
  message: string;
  message_type: 'text' | 'emoji' | 'system';
  created_at: string;
}

export const spaceService = {
  // Get live streams
  async getLiveStreams(channelId?: string): Promise<LiveStream[]> {
    try {
      const { data, error } = await supabase.rpc('get_live_streams', {
        p_channel_id: channelId || null
      });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching live streams:', error);
      return [];
    }
  },

  // Create a new live stream
  async createLiveStream(streamData: {
    title: string;
    description?: string;
    channel_id: string;
    host_profile_id: string;
    scheduled_start?: string;
    max_participants?: number;
    chat_enabled?: boolean;
    audience_can_request_mic?: boolean;
    is_recorded?: boolean;
  }): Promise<LiveStream | null> {
    try {
      const { data, error } = await supabase
        .from('live_streams')
        .insert([{
          ...streamData,
          status: streamData.scheduled_start ? 'scheduled' : 'live',
          actual_start: streamData.scheduled_start ? null : new Date().toISOString(),
        }])
        .select('*')
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating live stream:', error);
      return null;
    }
  },

  // Start a scheduled stream
  async startStream(streamId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('live_streams')
        .update({
          status: 'live',
          actual_start: new Date().toISOString(),
        })
        .eq('id', streamId);

      return !error;
    } catch (error) {
      console.error('Error starting stream:', error);
      return false;
    }
  },

  // End a live stream
  async endStream(streamId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('live_streams')
        .update({
          status: 'ended',
          ended_at: new Date().toISOString(),
        })
        .eq('id', streamId);

      return !error;
    } catch (error) {
      console.error('Error ending stream:', error);
      return false;
    }
  },

  // Join a live stream
  async joinStream(streamId: string, profileId: string, role: string = 'listener'): Promise<boolean> {
    try {
      console.log('🔗 Joining stream:', { streamId, profileId, role });
      const { data, error } = await supabase.rpc('join_live_stream', {
        p_stream_id: streamId,
        p_profile_id: profileId,
        p_role: role,
      });

      if (error) {
        console.error('❌ Error joining stream:', error);
        throw error;
      }
      console.log('✅ Join stream result:', data);
      return data === true;
    } catch (error) {
      console.error('💥 Error joining stream:', error);
      return false;
    }
  },

  // Leave a live stream
  async leaveStream(streamId: string, profileId: string): Promise<boolean> {
    try {
      console.log('🚪 Leaving stream:', { streamId, profileId });
      const { data, error } = await supabase.rpc('leave_live_stream', {
        p_stream_id: streamId,
        p_profile_id: profileId,
      });

      if (error) {
        console.error('❌ Error leaving stream:', error);
        throw error;
      }
      console.log('✅ Leave stream result:', data);
      return data === true;
    } catch (error) {
      console.error('💥 Error leaving stream:', error);
      return false;
    }
  },

  // Request microphone access
  async requestMicAccess(streamId: string, profileId: string): Promise<boolean> {
    try {
      console.log('🎤 Requesting mic access:', { streamId, profileId });
      const { data, error } = await supabase.rpc('request_mic_access', {
        p_stream_id: streamId,
        p_profile_id: profileId,
      });

      if (error) {
        console.error('❌ Error requesting mic access:', error);
        throw error;
      }
      console.log('✅ Request mic result:', data);
      return data === true;
    } catch (error) {
      console.error('💥 Error requesting mic access:', error);
      return false;
    }
  },

  // Grant microphone access (host only)
  async grantMicAccess(streamId: string, profileId: string, hostId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('grant_mic_access', {
        p_stream_id: streamId,
        p_profile_id: profileId,
        p_host_id: hostId,
      });

      if (error) throw error;
      return data === true;
    } catch (error) {
      console.error('Error granting mic access:', error);
      return false;
    }
  },

  // Get stream participants
  async getStreamParticipants(streamId: string): Promise<StreamParticipant[]> {
    try {
      const { data, error } = await supabase
        .from('stream_participants')
        .select('*')
        .eq('stream_id', streamId)
        .is('left_at', null)
        .order('joined_at', { ascending: true });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching stream participants:', error);
      return [];
    }
  },

  // Send chat message
  async sendChatMessage(streamId: string, profileId: string, message: string, messageType: string = 'text'): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('stream_chat')
        .insert([{
          stream_id: streamId,
          profile_id: profileId,
          message,
          message_type: messageType,
        }]);

      return !error;
    } catch (error) {
      console.error('Error sending chat message:', error);
      return false;
    }
  },

  // Get chat messages
  async getChatMessages(streamId: string, limit: number = 100): Promise<StreamChatMessage[]> {
    try {
      const { data, error } = await supabase
        .from('stream_chat')
        .select('*')
        .eq('stream_id', streamId)
        .order('created_at', { ascending: true })
        .limit(limit);

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      return [];
    }
  },

  // Subscribe to real-time updates for a stream
  subscribeToStream(streamId: string, callbacks: {
    onParticipantJoined?: (participant: StreamParticipant) => void;
    onParticipantLeft?: (participant: StreamParticipant) => void;
    onChatMessage?: (message: StreamChatMessage) => void;
    onStreamUpdate?: (stream: LiveStream) => void;
  }) {
    const participantsChannel = supabase
      .channel(`stream_participants_${streamId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'stream_participants',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          if (callbacks.onParticipantJoined) {
            callbacks.onParticipantJoined(payload.new as StreamParticipant);
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'stream_participants',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          if (payload.new.left_at && callbacks.onParticipantLeft) {
            callbacks.onParticipantLeft(payload.new as StreamParticipant);
          }
        }
      )
      .subscribe();

    const chatChannel = supabase
      .channel(`stream_chat_${streamId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'stream_chat',
          filter: `stream_id=eq.${streamId}`,
        },
        (payload) => {
          if (callbacks.onChatMessage) {
            callbacks.onChatMessage(payload.new as StreamChatMessage);
          }
        }
      )
      .subscribe();

    const streamChannel = supabase
      .channel(`live_stream_${streamId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'live_streams',
          filter: `id=eq.${streamId}`,
        },
        (payload) => {
          if (callbacks.onStreamUpdate) {
            callbacks.onStreamUpdate(payload.new as LiveStream);
          }
        }
      )
      .subscribe();

    return {
      unsubscribe: () => {
        supabase.removeChannel(participantsChannel);
        supabase.removeChannel(chatChannel);
        supabase.removeChannel(streamChannel);
      },
    };
  },
};