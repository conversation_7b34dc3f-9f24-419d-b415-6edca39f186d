import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';

export interface VoiceStory {
  id: string;
  profile_id: string;
  audio_url: string;
  audio_duration: number;
  transcript?: string;
  background_image_url?: string;
  background_color: string;
  story_type: 'voice' | 'voice_image' | 'voice_poll';
  title?: string;
  tags: string[];
  poll_question?: string;
  poll_options?: any;
  allow_replies: boolean;
  is_public: boolean;
  viewers_can_reply: boolean;
  created_at: string;
  expires_at: string;
  view_count: number;
  reply_count: number;
  has_viewed?: boolean;
}

export interface StoryReply {
  id: string;
  story_id: string;
  profile_id: string;
  audio_url: string;
  audio_duration: number;
  transcript?: string;
  created_at: string;
}

export interface CreateStoryData {
  audio_url: string;
  audio_duration: number;
  transcript?: string;
  background_image_url?: string;
  background_color?: string;
  story_type?: 'voice' | 'voice_image' | 'voice_poll';
  title?: string;
  tags?: string[];
  poll_question?: string;
  poll_options?: any;
  allow_replies?: boolean;
  is_public?: boolean;
  viewers_can_reply?: boolean;
  expires_at?: string;
  media_type?: 'image' | 'video'; // New field to distinguish between image and video
}

class StoryService {
  /**
   * Create a new voice story
   */
  async createStory(profileId: string, data: CreateStoryData): Promise<VoiceStory | null> {
    try {
      const storyData = {
        id: uuidv4(),
        profile_id: profileId,
        audio_url: data.audio_url,
        audio_duration: data.audio_duration,
        transcript: data.transcript,
        background_image_url: data.background_image_url,
        background_color: data.background_color || '#8B5CF6',
        story_type: data.story_type || 'voice',
        title: data.title,
        tags: data.tags || [],
        poll_question: data.poll_question,
        poll_options: data.poll_options,
        allow_replies: data.allow_replies !== false,
        is_public: data.is_public !== false,
        viewers_can_reply: data.viewers_can_reply !== false,
      };

      const { data: story, error } = await supabase
        .from('voice_stories')
        .insert(storyData)
        .select()
        .single();

      if (error) {
        console.error('Error creating story:', error);
        return null;
      }

      return story;
    } catch (error) {
      console.error('Error in createStory:', error);
      return null;
    }
  }

  /**
   * Get all active stories (not expired)
   */
  async getActiveStories(viewerId?: string): Promise<VoiceStory[]> {
    try {
      const { data: stories, error } = await supabase
        .rpc('get_active_stories', { viewer_id_param: viewerId });

      if (error) {
        console.error('Error fetching stories:', error);
        return [];
      }

      return stories || [];
    } catch (error) {
      console.error('Error in getActiveStories:', error);
      return [];
    }
  }

  /**
   * Get stories for a specific user
   */
  async getUserStories(profileId: string): Promise<VoiceStory[]> {
    try {
      const { data: stories, error } = await supabase
        .from('voice_stories')
        .select('*')
        .eq('profile_id', profileId)
        .gt('expires_at', new Date().toISOString())
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user stories:', error);
        return [];
      }

      return stories || [];
    } catch (error) {
      console.error('Error in getUserStories:', error);
      return [];
    }
  }

  /**
   * View a story (increment view count)
   */
  async viewStory(storyId: string, viewerId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .rpc('increment_story_view_count', {
          story_id_param: storyId,
          viewer_id_param: viewerId
        });

      if (error) {
        console.error('Error viewing story:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in viewStory:', error);
      return false;
    }
  }

  /**
   * Reply to a story
   */
  async replyToStory(
    storyId: string, 
    profileId: string, 
    audioUrl: string, 
    audioDuration: number, 
    transcript?: string
  ): Promise<StoryReply | null> {
    try {
      const replyData = {
        id: uuidv4(),
        story_id: storyId,
        profile_id: profileId,
        audio_url: audioUrl,
        audio_duration: audioDuration,
        transcript: transcript,
      };

      const { data: reply, error } = await supabase
        .from('story_replies')
        .insert(replyData)
        .select()
        .single();

      if (error) {
        console.error('Error creating story reply:', error);
        return null;
      }

      // Increment reply count
      await supabase
        .from('voice_stories')
        .update({ reply_count: supabase.sql`reply_count + 1` })
        .eq('id', storyId);

      return reply;
    } catch (error) {
      console.error('Error in replyToStory:', error);
      return null;
    }
  }

  /**
   * Get replies for a story
   */
  async getStoryReplies(storyId: string): Promise<StoryReply[]> {
    try {
      const { data: replies, error } = await supabase
        .from('story_replies')
        .select('*')
        .eq('story_id', storyId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching story replies:', error);
        return [];
      }

      return replies || [];
    } catch (error) {
      console.error('Error in getStoryReplies:', error);
      return [];
    }
  }

  /**
   * Delete a story
   */
  async deleteStory(storyId: string, profileId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('voice_stories')
        .delete()
        .eq('id', storyId)
        .eq('profile_id', profileId);

      if (error) {
        console.error('Error deleting story:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteStory:', error);
      return false;
    }
  }

  /**
   * Save story to highlights
   */
  async saveToHighlights(
    storyId: string, 
    profileId: string, 
    highlightName: string, 
    description?: string, 
    coverImageUrl?: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('story_highlights')
        .insert({
          profile_id: profileId,
          story_id: storyId,
          highlight_name: highlightName,
          highlight_description: description,
          cover_image_url: coverImageUrl,
        });

      if (error) {
        console.error('Error saving to highlights:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in saveToHighlights:', error);
      return false;
    }
  }
}

export const storyService = new StoryService();