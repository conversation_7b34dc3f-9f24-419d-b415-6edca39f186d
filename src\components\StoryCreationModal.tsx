import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Mic, MicOff, Image, Video, Camera, Download, Eye, EyeOff, MessageCircle, Clock, Palette, Music } from 'lucide-react';
import { toast } from 'sonner';
import { useRecord } from '@/hooks/use-record';
import { storyService, CreateStoryData } from '@/services/storyService';

interface StoryCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStoryCreated?: () => void;
  profileId: string;
}

export const StoryCreationModal: React.FC<StoryCreationModalProps> = ({
  isOpen,
  onClose,
  onStoryCreated,
  profileId
}) => {
  const { isRecording, startRecording, stopRecording, recordedBlob } = useRecord();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Enhanced form state
  const [activeTab, setActiveTab] = useState('voice');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [allowReplies, setAllowReplies] = useState(true);
  const [allowDownload, setAllowDownload] = useState(false);
  const [expiryHours, setExpiryHours] = useState('24');
  const [backgroundColor, setBackgroundColor] = useState('#8B5CF6');
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Media state
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [videoPreview, setVideoPreview] = useState<string | null>(null);

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast.error('Failed to start recording');
    }
  };

  const handleStopRecording = () => {
    stopRecording();
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error('Image size must be less than 10MB');
        return;
      }
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  const handleVideoSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        toast.error('Video size must be less than 50MB');
        return;
      }
      setSelectedVideo(file);
      const reader = new FileReader();
      reader.onload = (e) => setVideoPreview(e.target?.result as string);
      reader.readAsDataURL(file);
    }
  };

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim()) && tags.length < 5) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const backgroundColors = [
    '#8B5CF6', '#EF4444', '#F59E0B', '#10B981', '#3B82F6',
    '#8B5A2B', '#EC4899', '#6366F1', '#84CC16', '#F97316'
  ];

  const handleCreateStory = async () => {
    // Validation based on story type
    if (activeTab === 'voice' && !recordedBlob) {
      toast.error('Please record audio for your voice story');
      return;
    }
    if (activeTab === 'image' && !selectedImage) {
      toast.error('Please select an image for your story');
      return;
    }
    if (activeTab === 'video' && !selectedVideo) {
      toast.error('Please select a video for your story');
      return;
    }

    setIsCreating(true);

    try {
      let storyType: 'voice' | 'voice_image' | 'voice_poll' = 'voice';
      let audioUrl = '';
      let backgroundImageUrl = '';

      // Handle different story types
      if (activeTab === 'voice' && recordedBlob) {
        storyType = 'voice';
        audioUrl = URL.createObjectURL(recordedBlob);
      } else if (activeTab === 'image' && selectedImage) {
        storyType = 'voice_image';
        backgroundImageUrl = URL.createObjectURL(selectedImage);
        if (recordedBlob) {
          audioUrl = URL.createObjectURL(recordedBlob);
        }
      } else if (activeTab === 'video' && selectedVideo) {
        storyType = 'voice_image'; // We'll treat video as image with audio
        backgroundImageUrl = URL.createObjectURL(selectedVideo);
        if (recordedBlob) {
          audioUrl = URL.createObjectURL(recordedBlob);
        }
      }

      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + parseInt(expiryHours));

      const storyData: CreateStoryData = {
        audio_url: audioUrl || 'placeholder', // Some stories might not have audio
        audio_duration: 30, // Default duration
        title: title.trim() || undefined,
        story_type: storyType,
        background_color: backgroundColor,
        background_image_url: backgroundImageUrl || undefined,
        is_public: isPublic,
        allow_replies: allowReplies,
        viewers_can_reply: allowReplies,
        tags: tags,
        // Custom expiry time
        expires_at: expiryDate.toISOString(),
      };

      const story = await storyService.createStory(profileId, storyData);

      if (story) {
        toast.success(`${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} story created successfully!`);
        onStoryCreated?.();
        onClose();
        resetForm();
      } else {
        toast.error('Failed to create story');
      }
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setTags([]);
    setCurrentTag('');
    setSelectedImage(null);
    setSelectedVideo(null);
    setImagePreview(null);
    setVideoPreview(null);
    setActiveTab('voice');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Create Advanced Story
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="voice" className="flex items-center gap-2">
              <Mic className="h-4 w-4" />
              Voice
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              Image
            </TabsTrigger>
            <TabsTrigger value="video" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Video
            </TabsTrigger>
          </TabsList>

          <div className="space-y-6 mt-6">
            {/* Content Creation Tabs */}
            <TabsContent value="voice" className="space-y-4">
              <div className="space-y-4">
                <Label>Record Your Voice Story</Label>
                <div className="flex items-center gap-4">
                  {!isRecording ? (
                    <Button onClick={handleStartRecording} variant="default" size="lg">
                      <Mic className="h-5 w-5 mr-2" />
                      Start Recording
                    </Button>
                  ) : (
                    <Button onClick={handleStopRecording} variant="destructive" size="lg">
                      <MicOff className="h-5 w-5 mr-2" />
                      Stop Recording
                    </Button>
                  )}

                  {recordedBlob && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Music className="h-4 w-4" />
                      Audio recorded!
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="image" className="space-y-4">
              <div className="space-y-4">
                <Label>Select Image for Your Story</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {imagePreview ? (
                    <div className="space-y-4">
                      <img src={imagePreview} alt="Preview" className="max-h-48 mx-auto rounded-lg" />
                      <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                        Change Image
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Image className="h-12 w-12 mx-auto text-gray-400" />
                      <Button onClick={() => fileInputRef.current?.click()}>
                        Select Image
                      </Button>
                      <p className="text-sm text-gray-500">PNG, JPG up to 10MB</p>
                    </div>
                  )}
                </div>

                {/* Optional voice overlay for image */}
                <div className="space-y-2">
                  <Label>Add Voice Narration (Optional)</Label>
                  <div className="flex items-center gap-4">
                    {!isRecording ? (
                      <Button onClick={handleStartRecording} variant="outline" size="sm">
                        <Mic className="h-4 w-4 mr-2" />
                        Record Voice
                      </Button>
                    ) : (
                      <Button onClick={handleStopRecording} variant="destructive" size="sm">
                        <MicOff className="h-4 w-4 mr-2" />
                        Stop
                      </Button>
                    )}
                    {recordedBlob && (
                      <span className="text-sm text-green-600">Voice added!</span>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="video" className="space-y-4">
              <div className="space-y-4">
                <Label>Select Video for Your Story</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {videoPreview ? (
                    <div className="space-y-4">
                      <video src={videoPreview} className="max-h-48 mx-auto rounded-lg" controls />
                      <Button variant="outline" onClick={() => videoInputRef.current?.click()}>
                        Change Video
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Video className="h-12 w-12 mx-auto text-gray-400" />
                      <Button onClick={() => videoInputRef.current?.click()}>
                        Select Video
                      </Button>
                      <p className="text-sm text-gray-500">MP4, MOV up to 50MB</p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Hidden file inputs */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageSelect}
              className="hidden"
            />
            <input
              ref={videoInputRef}
              type="file"
              accept="video/*"
              onChange={handleVideoSelect}
              className="hidden"
            />

            {/* Common Settings */}
            <div className="space-y-4 border-t pt-4">
              {/* Title and Description */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Give your story a catchy title..."
                    maxLength={100}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Add a description..."
                    maxLength={500}
                    rows={3}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags (Max 5)</Label>
                <div className="flex gap-2 flex-wrap mb-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                      #{tag} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="Add a tag..."
                    maxLength={20}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button onClick={addTag} variant="outline" size="sm" disabled={tags.length >= 5}>
                    Add
                  </Button>
                </div>
              </div>

              {/* Background Color */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Background Color
                </Label>
                <div className="flex gap-2 flex-wrap">
                  {backgroundColors.map((color) => (
                    <button
                      key={color}
                      className={`w-8 h-8 rounded-full border-2 ${backgroundColor === color ? 'border-gray-800' : 'border-gray-300'}`}
                      style={{ backgroundColor: color }}
                      onClick={() => setBackgroundColor(color)}
                    />
                  ))}
                </div>
              </div>

              {/* Advanced Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Expires After
                  </Label>
                  <Select value={expiryHours} onValueChange={setExpiryHours}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Hour</SelectItem>
                      <SelectItem value="6">6 Hours</SelectItem>
                      <SelectItem value="12">12 Hours</SelectItem>
                      <SelectItem value="24">24 Hours</SelectItem>
                      <SelectItem value="48">48 Hours</SelectItem>
                      <SelectItem value="168">1 Week</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Privacy and Interaction Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="isPublic" className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Public Story
                  </Label>
                  <Switch
                    id="isPublic"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowReplies" className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    Allow Replies
                  </Label>
                  <Switch
                    id="allowReplies"
                    checked={allowReplies}
                    onCheckedChange={setAllowReplies}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowDownload" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Allow Download
                  </Label>
                  <Switch
                    id="allowDownload"
                    checked={allowDownload}
                    onCheckedChange={setAllowDownload}
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 pt-4 border-t">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateStory}
                disabled={isCreating}
                className="flex-1"
              >
                {isCreating ? 'Creating...' : `Create ${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Story`}
              </Button>
            </div>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};