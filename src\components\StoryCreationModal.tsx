import React, { useState, useRef } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Mic, MicOff, Image, Video, Camera, Download, Eye, EyeOff, MessageCircle, Clock, Palette, Music } from 'lucide-react';
import { toast } from 'sonner';
import { useRecord } from '@/hooks/use-record';
import { storyService, CreateStoryData } from '@/services/storyService';
import { supabase } from '@/integrations/supabase/client';

interface StoryCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStoryCreated?: () => void;
  profileId: string;
}

export const StoryCreationModal: React.FC<StoryCreationModalProps> = ({
  isOpen,
  onClose,
  onStoryCreated,
  profileId
}) => {
  const { isRecording, startRecording, stopRecording, recordedBlob } = useRecord();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoInputRef = useRef<HTMLInputElement>(null);

  // Enhanced form state
  const [activeTab, setActiveTab] = useState('voice');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [allowReplies, setAllowReplies] = useState(true);
  const [allowDownload, setAllowDownload] = useState(false);
  const [expiryHours, setExpiryHours] = useState('24');
  const [backgroundColor, setBackgroundColor] = useState('#8B5CF6');
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  // Enhanced media state for batch uploads
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [filePreviews, setFilePreviews] = useState<string[]>([]);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [voiceRecordings, setVoiceRecordings] = useState<{[key: number]: Blob}>({});
  const [storyTitles, setStoryTitles] = useState<{[key: number]: string}>({});
  const [storyDescriptions, setStoryDescriptions] = useState<{[key: number]: string}>({});

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast.error('Failed to start recording');
    }
  };

  const handleStopRecording = () => {
    stopRecording();
    // If we're in image/video mode and have a recorded blob, save it for the current file
    if ((activeTab === 'image' || activeTab === 'video') && recordedBlob && selectedFiles.length > 0) {
      handleVoiceRecordingForFile(currentFileIndex, recordedBlob);
    }
  };

  const handleMultipleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    const validFiles: File[] = [];
    const previews: string[] = [];

    files.forEach((file) => {
      const isImage = file.type.startsWith('image/');
      const isVideo = file.type.startsWith('video/');

      if (!isImage && !isVideo) {
        toast.error(`${file.name} is not a valid image or video file`);
        return;
      }

      const sizeLimit = isVideo ? 50 * 1024 * 1024 : 10 * 1024 * 1024; // 50MB for video, 10MB for image
      if (file.size > sizeLimit) {
        toast.error(`${file.name} is too large. Max size: ${isVideo ? '50MB' : '10MB'}`);
        return;
      }

      validFiles.push(file);

      const reader = new FileReader();
      reader.onload = (e) => {
        previews.push(e.target?.result as string);
        if (previews.length === validFiles.length) {
          setSelectedFiles(validFiles);
          setFilePreviews(previews);
          setCurrentFileIndex(0);
        }
      };
      reader.readAsDataURL(file);
    });
  };

  const handleVoiceRecordingForFile = (fileIndex: number, blob: Blob) => {
    setVoiceRecordings(prev => ({
      ...prev,
      [fileIndex]: blob
    }));
  };

  const getCurrentFile = () => selectedFiles[currentFileIndex];
  const getCurrentPreview = () => filePreviews[currentFileIndex];
  const getCurrentVoiceRecording = () => voiceRecordings[currentFileIndex];
  const isCurrentFileVideo = () => getCurrentFile()?.type.startsWith('video/');

  const nextFile = () => {
    if (currentFileIndex < selectedFiles.length - 1) {
      setCurrentFileIndex(currentFileIndex + 1);
    }
  };

  const previousFile = () => {
    if (currentFileIndex > 0) {
      setCurrentFileIndex(currentFileIndex - 1);
    }
  };

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim()) && tags.length < 5) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const backgroundColors = [
    '#8B5CF6', '#EF4444', '#F59E0B', '#10B981', '#3B82F6',
    '#8B5A2B', '#EC4899', '#6366F1', '#84CC16', '#F97316'
  ];

  const handleCreateStories = async () => {
    // Validation
    if (activeTab === 'voice' && !recordedBlob) {
      toast.error('Please record audio for your voice story');
      return;
    }
    if ((activeTab === 'image' || activeTab === 'video') && selectedFiles.length === 0) {
      toast.error(`Please select ${activeTab === 'image' ? 'images' : 'videos'} for your stories`);
      return;
    }

    setIsCreating(true);

    try {
      const expiryDate = new Date();
      expiryDate.setHours(expiryDate.getHours() + parseInt(expiryHours));

      let createdCount = 0;

      if (activeTab === 'voice') {
        // Single voice story
        const storyData: CreateStoryData = {
          audio_url: URL.createObjectURL(recordedBlob!),
          audio_duration: 30,
          title: title.trim() || undefined,
          story_type: 'voice',
          background_color: backgroundColor,
          is_public: isPublic,
          allow_replies: allowReplies,
          viewers_can_reply: allowReplies,
          tags: tags,
          expires_at: expiryDate.toISOString(),
        };

        const story = await storyService.createStory(profileId, storyData);
        if (story) createdCount++;
      } else {
        // Multiple media stories
        for (let i = 0; i < selectedFiles.length; i++) {
          const file = selectedFiles[i];
          const isVideo = file.type.startsWith('video/');
          const voiceBlob = voiceRecordings[i];

          const storyData: CreateStoryData = {
            audio_url: voiceBlob ? URL.createObjectURL(voiceBlob) : (isVideo ? 'video-audio' : 'no-audio'),
            audio_duration: voiceBlob ? 30 : (isVideo ? 60 : 0),
            title: storyTitles[i] || title.trim() || undefined,
            story_type: isVideo ? 'voice_image' : 'voice_image', // Both use voice_image type
            background_color: backgroundColor,
            background_image_url: URL.createObjectURL(file),
            is_public: isPublic,
            allow_replies: allowReplies,
            viewers_can_reply: allowReplies,
            tags: tags,
            expires_at: expiryDate.toISOString(),
            media_type: isVideo ? 'video' : 'image', // Custom field to distinguish
          };

          const story = await storyService.createStory(profileId, storyData);
          if (story) createdCount++;
        }
      }

      if (createdCount > 0) {
        toast.success(`${createdCount} ${createdCount === 1 ? 'story' : 'stories'} created successfully!`);
        onStoryCreated?.();
        onClose();
        resetForm();
      } else {
        toast.error('Failed to create stories');
      }
    } catch (error) {
      console.error('Error creating stories:', error);
      toast.error('Failed to create stories');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setTags([]);
    setCurrentTag('');
    setSelectedFiles([]);
    setFilePreviews([]);
    setCurrentFileIndex(0);
    setVoiceRecordings({});
    setStoryTitles({});
    setStoryDescriptions({});
    setActiveTab('voice');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Create Advanced Story
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="voice" className="flex items-center gap-2">
              <Mic className="h-4 w-4" />
              Voice
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-2">
              <Image className="h-4 w-4" />
              Image
            </TabsTrigger>
            <TabsTrigger value="video" className="flex items-center gap-2">
              <Video className="h-4 w-4" />
              Video
            </TabsTrigger>
          </TabsList>

          <div className="space-y-6 mt-6">
            {/* Content Creation Tabs */}
            <TabsContent value="voice" className="space-y-4">
              <div className="space-y-4">
                <Label>Record Your Voice Story</Label>
                <div className="flex items-center gap-4">
                  {!isRecording ? (
                    <Button onClick={handleStartRecording} variant="default" size="lg">
                      <Mic className="h-5 w-5 mr-2" />
                      Start Recording
                    </Button>
                  ) : (
                    <Button onClick={handleStopRecording} variant="destructive" size="lg">
                      <MicOff className="h-5 w-5 mr-2" />
                      Stop Recording
                    </Button>
                  )}

                  {recordedBlob && (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Music className="h-4 w-4" />
                      Audio recorded!
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="image" className="space-y-4">
              <div className="space-y-4">
                <Label>Select Images for Your Stories</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {selectedFiles.length > 0 ? (
                    <div className="space-y-4">
                      {/* Current image preview */}
                      <div className="relative">
                        <img
                          src={getCurrentPreview()}
                          alt="Preview"
                          className="max-h-64 w-full object-contain mx-auto rounded-lg"
                        />

                        {/* Navigation for multiple files */}
                        {selectedFiles.length > 1 && (
                          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/50 rounded-full px-3 py-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={previousFile}
                              disabled={currentFileIndex === 0}
                              className="text-white hover:text-gray-300"
                            >
                              ←
                            </Button>
                            <span className="text-white text-sm">
                              {currentFileIndex + 1} / {selectedFiles.length}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={nextFile}
                              disabled={currentFileIndex === selectedFiles.length - 1}
                              className="text-white hover:text-gray-300"
                            >
                              →
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                          Add More Images
                        </Button>
                        <Button variant="outline" onClick={() => setSelectedFiles([])}>
                          Clear All
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Image className="h-12 w-12 mx-auto text-gray-400" />
                      <Button onClick={() => fileInputRef.current?.click()}>
                        Select Images
                      </Button>
                      <p className="text-sm text-gray-500">PNG, JPG up to 10MB each. Select multiple files!</p>
                    </div>
                  )}
                </div>

                {/* Voice narration for current image */}
                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>Add Voice Narration for Current Image (Optional)</Label>
                    <div className="flex items-center gap-4">
                      {!isRecording ? (
                        <Button onClick={handleStartRecording} variant="outline" size="sm">
                          <Mic className="h-4 w-4 mr-2" />
                          Record Voice
                        </Button>
                      ) : (
                        <Button onClick={handleStopRecording} variant="destructive" size="sm">
                          <MicOff className="h-4 w-4 mr-2" />
                          Stop
                        </Button>
                      )}
                      {getCurrentVoiceRecording() && (
                        <span className="text-sm text-green-600">Voice added for this image!</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="video" className="space-y-4">
              <div className="space-y-4">
                <Label>Select Videos for Your Stories</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  {selectedFiles.length > 0 ? (
                    <div className="space-y-4">
                      {/* Current video preview */}
                      <div className="relative">
                        <video
                          src={getCurrentPreview()}
                          className="max-h-64 w-full object-contain mx-auto rounded-lg"
                          controls
                          muted
                        />

                        {/* Navigation for multiple files */}
                        {selectedFiles.length > 1 && (
                          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-black/50 rounded-full px-3 py-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={previousFile}
                              disabled={currentFileIndex === 0}
                              className="text-white hover:text-gray-300"
                            >
                              ←
                            </Button>
                            <span className="text-white text-sm">
                              {currentFileIndex + 1} / {selectedFiles.length}
                            </span>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={nextFile}
                              disabled={currentFileIndex === selectedFiles.length - 1}
                              className="text-white hover:text-gray-300"
                            >
                              →
                            </Button>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <Button variant="outline" onClick={() => videoInputRef.current?.click()}>
                          Add More Videos
                        </Button>
                        <Button variant="outline" onClick={() => setSelectedFiles([])}>
                          Clear All
                        </Button>
                      </div>

                      <p className="text-sm text-muted-foreground">
                        Videos already have audio - no voice recording needed!
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Video className="h-12 w-12 mx-auto text-gray-400" />
                      <Button onClick={() => videoInputRef.current?.click()}>
                        Select Videos
                      </Button>
                      <p className="text-sm text-gray-500">MP4, MOV up to 50MB each. Select multiple files!</p>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            {/* Hidden file inputs */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleMultipleFileSelect}
              className="hidden"
            />
            <input
              ref={videoInputRef}
              type="file"
              accept="video/*"
              multiple
              onChange={handleMultipleFileSelect}
              className="hidden"
            />

            {/* Common Settings */}
            <div className="space-y-4 border-t pt-4">
              {/* Title and Description */}
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Give your story a catchy title..."
                    maxLength={100}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Description (Optional)</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Add a description..."
                    maxLength={500}
                    rows={3}
                  />
                </div>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags (Max 5)</Label>
                <div className="flex gap-2 flex-wrap mb-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeTag(tag)}>
                      #{tag} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="Add a tag..."
                    maxLength={20}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button onClick={addTag} variant="outline" size="sm" disabled={tags.length >= 5}>
                    Add
                  </Button>
                </div>
              </div>

              {/* Background Color */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Palette className="h-4 w-4" />
                  Background Color
                </Label>
                <div className="flex gap-2 flex-wrap">
                  {backgroundColors.map((color) => (
                    <button
                      key={color}
                      className={`w-8 h-8 rounded-full border-2 ${backgroundColor === color ? 'border-gray-800' : 'border-gray-300'}`}
                      style={{ backgroundColor: color }}
                      onClick={() => setBackgroundColor(color)}
                    />
                  ))}
                </div>
              </div>

              {/* Advanced Settings */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Expires After
                  </Label>
                  <Select value={expiryHours} onValueChange={setExpiryHours}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Hour</SelectItem>
                      <SelectItem value="6">6 Hours</SelectItem>
                      <SelectItem value="12">12 Hours</SelectItem>
                      <SelectItem value="24">24 Hours</SelectItem>
                      <SelectItem value="48">48 Hours</SelectItem>
                      <SelectItem value="168">1 Week</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Privacy and Interaction Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="isPublic" className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Public Story
                  </Label>
                  <Switch
                    id="isPublic"
                    checked={isPublic}
                    onCheckedChange={setIsPublic}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowReplies" className="flex items-center gap-2">
                    <MessageCircle className="h-4 w-4" />
                    Allow Replies
                  </Label>
                  <Switch
                    id="allowReplies"
                    checked={allowReplies}
                    onCheckedChange={setAllowReplies}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="allowDownload" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Allow Download
                  </Label>
                  <Switch
                    id="allowDownload"
                    checked={allowDownload}
                    onCheckedChange={setAllowDownload}
                  />
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-2 pt-4 border-t">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={handleCreateStories}
                disabled={isCreating}
                className="flex-1"
              >
                {isCreating ? 'Creating...' :
                  activeTab === 'voice' ? 'Create Voice Story' :
                  `Create ${selectedFiles.length} ${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} ${selectedFiles.length === 1 ? 'Story' : 'Stories'}`
                }
              </Button>
            </div>
          </div>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};