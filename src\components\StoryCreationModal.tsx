import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Mic, MicOff } from 'lucide-react';
import { toast } from 'sonner';
import { useRecord } from '@/hooks/use-record';
import { storyService, CreateStoryData } from '@/services/storyService';

interface StoryCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStoryCreated?: () => void;
  profileId: string;
}

export const StoryCreationModal: React.FC<StoryCreationModalProps> = ({
  isOpen,
  onClose,
  onStoryCreated,
  profileId
}) => {
  const { isRecording, startRecording, stopRecording, recordedBlob } = useRecord();
  
  // Form state
  const [title, setTitle] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [isCreating, setIsCreating] = useState(false);

  const handleStartRecording = async () => {
    try {
      await startRecording();
    } catch (error) {
      console.error('Failed to start recording:', error);
      toast.error('Failed to start recording');
    }
  };

  const handleStopRecording = () => {
    stopRecording();
  };

  const handleCreateStory = async () => {
    if (!recordedBlob) {
      toast.error('Please record audio for your story');
      return;
    }

    setIsCreating(true);

    try {
      // Create a temporary URL for the audio blob
      const audioUrl = URL.createObjectURL(recordedBlob);
      
      const storyData: CreateStoryData = {
        audio_url: audioUrl,
        audio_duration: 30, // Default duration
        title: title.trim() || undefined,
        story_type: 'voice',
        background_color: '#8B5CF6',
        is_public: isPublic,
        allow_replies: true,
        viewers_can_reply: true,
      };

      const story = await storyService.createStory(profileId, storyData);
      
      if (story) {
        toast.success('Story created successfully!');
        onStoryCreated?.();
        onClose();
        // Reset form
        setTitle('');
      } else {
        toast.error('Failed to create story');
      }
    } catch (error) {
      console.error('Error creating story:', error);
      toast.error('Failed to create story');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Create Voice Story</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Audio Recording */}
          <div className="space-y-4">
            <Label>Record Your Voice Story</Label>
            <div className="flex items-center gap-4">
              {!isRecording ? (
                <Button onClick={handleStartRecording} variant="default" size="lg">
                  <Mic className="h-5 w-5 mr-2" />
                  Start Recording
                </Button>
              ) : (
                <Button onClick={handleStopRecording} variant="destructive" size="lg">
                  <MicOff className="h-5 w-5 mr-2" />
                  Stop Recording
                </Button>
              )}
              
              {recordedBlob && (
                <div className="text-sm text-muted-foreground">
                  Audio recorded!
                </div>
              )}
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title (Optional)</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Give your story a title..."
              maxLength={100}
            />
          </div>

          {/* Settings */}
          <div className="flex items-center justify-between">
            <Label htmlFor="isPublic">Public Story</Label>
            <Switch
              id="isPublic"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateStory}
              disabled={!recordedBlob || isCreating}
            >
              {isCreating ? 'Creating...' : 'Create Story'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};