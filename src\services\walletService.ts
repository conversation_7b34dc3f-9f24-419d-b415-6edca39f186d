import { ethers } from 'ethers';
import * as web3 from '@solana/web3.js';
import { HELIUS_API_KEY } from '@/utils/env';

// Interface for wallet data
export interface WalletData {
  address: string;
  privateKey: string;
  balance: string;
  network: 'base' | 'base-goerli';
  encryptedKey?: string; // For enhanced security
  hasBackup?: boolean; // Whether the wallet has been backed up
  tokenBalances?: Record<string, string>; // Token balances
  // Add Solana wallet data
  solana?: {
    address: string;
    secretKey?: string; // Base64 encoded secret key
    publicKey?: string;
    network: string; // mainnet-beta, testnet, devnet
    balance: string;
    tokenBalances?: Record<string, string>;
  };
}

// Interface for transaction data
export interface TransactionData {
  id: string;
  from: string;
  to: string;
  amount: string;
  timestamp: Date;
  status: 'pending' | 'completed' | 'failed';
  txHash?: string;
  type: 'tip' | 'deposit' | 'withdrawal' | 'funding' | 'token_transfer';
  message?: string;
  token?: TokenData; // For token transfers
  blockchain?: 'ethereum' | 'solana'; // Add blockchain identifier
}

// Interface for token data
export interface TokenData {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  balance?: string;
  logoUrl?: string;
  blockchain?: 'ethereum' | 'solana'; // Add blockchain identifier
}

// Base network configuration with fallback RPC URLs
const networks = {
  'base': {
    name: 'Base Mainnet',
    chainId: 8453,
    rpcUrls: [
      'https://mainnet.base.org',
      'https://base-mainnet.public.blastapi.io',
      'https://1rpc.io/base'
    ],
    blockExplorer: 'https://basescan.org',
    tokenSymbol: 'ETH',
    currentRpcIndex: 0
  },
  'base-goerli': {
    name: 'Base Goerli Testnet',
    chainId: 84531,
    rpcUrls: [
      'https://goerli.base.org',
      'https://base-goerli.public.blastapi.io'
    ],
    blockExplorer: 'https://goerli.basescan.org',
    tokenSymbol: 'ETH',
    currentRpcIndex: 0
  }
};

// Solana network configuration with Helius API - ensure we're using the correct key
const solanaNetworks = {
  'mainnet-beta': {
    name: 'Solana Mainnet',
    rpcUrl: `https://mainnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}`,
    blockExplorer: 'https://explorer.solana.com',
    tokenSymbol: 'SOL'
  },
  'testnet': {
    name: 'Solana Testnet',
    rpcUrl: `https://testnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}`,
    blockExplorer: 'https://explorer.solana.com',
    tokenSymbol: 'SOL'
  },
  'devnet': {
    name: 'Solana Devnet',
    rpcUrl: `https://devnet.helius-rpc.com/?api-key=${HELIUS_API_KEY}`,
    blockExplorer: 'https://explorer.solana.com',
    tokenSymbol: 'SOL'
  }
};

// Common tokens on Base network
const commonTokens: Record<string, TokenData> = {
  'USDC': {
    address: '******************************************', // Base Mainnet USDC
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    logoUrl: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
    blockchain: 'ethereum'
  },
  'DAI': {
    address: '******************************************', // Base Mainnet DAI
    symbol: 'DAI',
    name: 'Dai Stablecoin',
    decimals: 18,
    logoUrl: 'https://cryptologos.cc/logos/multi-collateral-dai-dai-logo.png',
    blockchain: 'ethereum'
  },
  // Add Solana tokens
  'SOL_USDC': {
    address: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Solana Mainnet USDC
    symbol: 'USDC',
    name: 'USD Coin (Solana)',
    decimals: 6,
    logoUrl: 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
    blockchain: 'solana'
  }
};

// Use mainnet for real transactions
const DEFAULT_NETWORK = 'base';
const DEFAULT_SOLANA_NETWORK = 'mainnet-beta';

// Create a new random wallet (only for testing)
const createRandomWallet = (): WalletData => {
  const wallet = ethers.Wallet.createRandom();

  return {
    address: wallet.address,
    privateKey: wallet.privateKey,
    balance: '0.0',
    network: DEFAULT_NETWORK,
    hasBackup: false,
    tokenBalances: {}
  };
};

// Create a deterministic wallet from a user ID
const createDeterministicWallet = (userId: string): WalletData => {
  try {
    // Create a deterministic wallet using the user ID as the seed
    // This ensures the same user always gets the same wallet
    const privateKey = generateDeterministicPrivateKey(userId);
    const wallet = new ethers.Wallet(privateKey);

    return {
      address: wallet.address,
      privateKey: wallet.privateKey,
      balance: '0.0',
      network: DEFAULT_NETWORK,
      hasBackup: false,
      tokenBalances: {}
    };
  } catch (error) {
    console.error('Error creating deterministic wallet:', error);
    // Fallback to random wallet in case of error
    return createRandomWallet();
  }
};

// Generate a deterministic private key from a user ID
const generateDeterministicPrivateKey = (userId: string): string => {
  // Convert the user ID to a 32-byte array using SHA-256
  // This ensures we get a consistent private key for the same user ID
  const hash = ethers.utils.id(userId); // keccak256 hash

  // Ensure it's a valid private key (32 bytes)
  // Private keys must be 32 bytes (64 hex characters) without 0x prefix
  return hash;
};

// Create a wallet - this is the main function that should be used
const createWallet = (userId?: string): WalletData => {
  if (userId) {
    // If a user ID is provided, create a deterministic wallet
    return createDeterministicWallet(userId);
  } else {
    // If no user ID is provided, create a random wallet
    return createRandomWallet();
  }
};

// Get provider with fallback and retry mechanism
const getProvider = (network: 'base' | 'base-goerli' = DEFAULT_NETWORK) => {
  const networkConfig = networks[network];
  const rpcUrl = networkConfig.rpcUrls[networkConfig.currentRpcIndex];
  
  const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
  
  // Add error handler for rate limiting
  const originalFetch = provider.fetch.bind(provider);
  provider.fetch = async (url: string, json: string) => {
    try {
      return await originalFetch(url, json);
    } catch (error: any) {
      // If we hit rate limits (429 errors)
      if (error?.status === 429 || error?.statusCode === 429 ||
          (typeof error?.message === 'string' && error.message.includes('429'))) {
        console.warn(`RPC rate limit hit for ${rpcUrl}. Trying next available RPC...`);
        
        // Try next RPC in the list
        networkConfig.currentRpcIndex = (networkConfig.currentRpcIndex + 1) % networkConfig.rpcUrls.length;
        const newRpcUrl = networkConfig.rpcUrls[networkConfig.currentRpcIndex];
        
        console.log(`Switching to alternative RPC: ${newRpcUrl}`);
        const newProvider = new ethers.providers.JsonRpcProvider(newRpcUrl);
        
        // Try the request with the new provider
        return newProvider.fetch(url, json);
      }
      
      // For other errors, just throw
      throw error;
    }
  };
  
  return provider;
};

// Get wallet instance from wallet data
const getWalletInstance = (walletData: WalletData) => {
  const provider = getProvider(walletData.network);
  return new ethers.Wallet(walletData.privateKey, provider);
};

// Get wallet balance - updated to use real provider
const getWalletBalance = async (walletData: WalletData): Promise<string> => {
  try {
    // Use ethers provider to get real balance
    const provider = getProvider(walletData.network);
    const balanceWei = await provider.getBalance(walletData.address);
    
    // Convert from wei to ether
    const balanceEther = ethers.utils.formatEther(balanceWei);
    
    console.log(`Real balance for ${walletData.address}: ${balanceEther} ETH`);
    return balanceEther;
  } catch (error) {
    console.error('Error getting real wallet balance:', error);
    return walletData.balance; // Fall back to stored balance on error
  }
};

// Get the balance of an address - updated to use real provider
const getBalance = async (address: string): Promise<string> => {
  try {
    const provider = getProvider();
    const balanceWei = await provider.getBalance(address);
    
    // Convert from wei to ether
    return ethers.utils.formatEther(balanceWei);
  } catch (error) {
    console.error('Error getting address balance:', error);
    return '0.0'; // Default to zero on error
  }
};

// Send a transaction - updated to use real provider
const sendTransaction = async (
  from: string,
  to: string,
  amount: string,
  privateKey: string
): Promise<{ success: boolean; hash?: string; error?: string }> => {
  try {
    const provider = getProvider();
    const wallet = new ethers.Wallet(privateKey, provider);
    
    // Convert amount from ether to wei
    const amountWei = ethers.utils.parseEther(amount);
    
    // Create transaction
    const tx = {
      to,
      value: amountWei
    };
    
    // Send transaction
    const response = await wallet.sendTransaction(tx);
    
    // Wait for transaction to be mined
    const receipt = await response.wait();
    
    return {
      success: true,
      hash: receipt.transactionHash
    };
  } catch (error) {
    console.error('Error sending transaction:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transaction failed'
    };
  }
};

// Send a tip
const sendTip = async (
  senderWallet: WalletData,
  recipientAddress: string,
  amount: string,
  message: string = ''
): Promise<TransactionData> => {
  try {
    console.log(`Sending tip of ${amount} from ${senderWallet.address} to ${recipientAddress}`);

    // Check if we have private key (needed for real transaction)
    if (!senderWallet.privateKey) {
      throw new Error('Private key not available for this wallet');
    }

    // Get real balance to verify sufficient funds
    const currentBalance = await getWalletBalance(senderWallet);
    if (parseFloat(amount) > parseFloat(currentBalance)) {
      throw new Error('Insufficient balance');
    }

    // Send the actual transaction
    const { success, hash, error } = await sendTransaction(
      senderWallet.address,
      recipientAddress,
      amount,
      senderWallet.privateKey
    );

    if (!success || !hash) {
      throw new Error(error || 'Transaction failed');
    }

    // Create transaction data
    const transactionData: TransactionData = {
      id: `tx-${Date.now()}`,
      from: senderWallet.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'completed',
      txHash: hash,
      type: 'tip',
      message
    };

    return transactionData;
  } catch (error) {
    console.error('Error sending tip:', error);

    // Create failed transaction data
    const transactionData: TransactionData = {
      id: `tx-${Date.now()}`,
      from: senderWallet.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'failed',
      type: 'tip',
      message
    };

    return transactionData;
  }
};

// Get transaction URL for block explorer
const getTransactionUrl = (
  txHash: string,
  network: 'base' | 'base-goerli' = DEFAULT_NETWORK
) => {
  return `${networks[network].blockExplorer}/tx/${txHash}`;
};

// Get address URL for block explorer
const getAddressUrl = (
  address: string,
  network: 'base' | 'base-goerli' = DEFAULT_NETWORK
) => {
  return `${networks[network].blockExplorer}/address/${address}`;
};

// Format address for display (0x1234...5678)
const formatAddress = (address: string): string => {
  if (!address || address.length < 10) return address;
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
};

// Get token balance
const getTokenBalance = async (
  walletData: WalletData,
  tokenAddress: string,
  tokenDecimals: number = 18
): Promise<string> => {
  try {
    const provider = getProvider(walletData.network);
    
    // Standard ERC20 ABI for balanceOf function
    const tokenAbi = [
      "function balanceOf(address owner) view returns (uint256)",
      "function decimals() view returns (uint8)"
    ];
    
    const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, provider);
    
    // Get actual decimals from contract
    let decimals = tokenDecimals;
    try {
      decimals = await tokenContract.decimals();
    } catch (err) {
      console.log('Could not get decimals from token contract, using provided value');
    }
    
    // Get token balance
    const balance = await tokenContract.balanceOf(walletData.address);
    const formattedBalance = ethers.utils.formatUnits(balance, decimals);
    
    console.log(`Token balance for ${tokenAddress} in wallet ${walletData.address}: ${formattedBalance}`);
    return formattedBalance;
  } catch (error) {
    console.error('Error getting token balance:', error);
    return '0';
  }
};

// Send token
const sendToken = async (
  senderWallet: WalletData,
  recipientAddress: string,
  tokenAddress: string,
  amount: string,
  tokenDecimals: number = 18,
  message: string = ''
): Promise<TransactionData> => {
  try {
    console.log(`Sending ${amount} tokens from ${senderWallet.address} to ${recipientAddress}`);

    // Get token details from common tokens or use defaults
    const token = Object.values(commonTokens).find(t => t.address.toLowerCase() === tokenAddress.toLowerCase()) || {
      address: tokenAddress,
      symbol: 'TOKEN',
      name: 'Unknown Token',
      decimals: tokenDecimals
    };

    const provider = getProvider(senderWallet.network);
    const wallet = new ethers.Wallet(senderWallet.privateKey, provider);
    
    // ERC20 token interface
    const tokenAbi = [
      "function transfer(address to, uint amount) returns (bool)",
      "function balanceOf(address owner) view returns (uint256)",
      "function decimals() view returns (uint8)"
    ];
    
    const tokenContract = new ethers.Contract(tokenAddress, tokenAbi, wallet);
    
    // Get actual decimals from contract
    let decimals = tokenDecimals;
    try {
      decimals = await tokenContract.decimals();
    } catch (err) {
      console.log('Could not get decimals from token contract, using provided value');
    }
    
    // Check balance
    const balance = await tokenContract.balanceOf(senderWallet.address);
    const formattedBalance = ethers.utils.formatUnits(balance, decimals);
    
    if (parseFloat(formattedBalance) < parseFloat(amount)) {
      throw new Error('Insufficient token balance');
    }
    
    // Convert amount to token units
    const amountInTokenUnits = ethers.utils.parseUnits(amount, decimals);
    
    // Send transaction
    const tx = await tokenContract.transfer(recipientAddress, amountInTokenUnits);
    const receipt = await tx.wait();
    
    // Create transaction data
    const transactionData: TransactionData = {
      id: `tx-${Date.now()}`,
      from: senderWallet.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'completed',
      txHash: receipt.transactionHash,
      type: 'token_transfer',
      message,
      token
    };

    return transactionData;
  } catch (error) {
    console.error('Error sending token:', error);

    // Create failed transaction data
    const transactionData: TransactionData = {
      id: `tx-${Date.now()}`,
      from: senderWallet.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'failed',
      type: 'token_transfer',
      message,
      token: {
        address: tokenAddress,
        symbol: 'TOKEN',
        name: 'Unknown Token',
        decimals: tokenDecimals
      }
    };

    return transactionData;
  }
};

// Generate QR code data for wallet address
const generateAddressQRData = (address: string): string => {
  return `ethereum:${address}`;
};

// Encrypt private key with password
const encryptWallet = async (
  walletData: WalletData,
  password: string
): Promise<WalletData> => {
  try {
    const wallet = new ethers.Wallet(walletData.privateKey);
    const encryptedKeyJson = await wallet.encrypt(password);

    return {
      ...walletData,
      encryptedKey: encryptedKeyJson
    };
  } catch (error) {
    console.error('Error encrypting wallet:', error);
    return walletData;
  }
};

// Decrypt private key with password
const decryptWallet = async (
  encryptedKey: string,
  password: string
): Promise<string | null> => {
  try {
    const wallet = await ethers.Wallet.fromEncryptedJson(encryptedKey, password);
    return wallet.privateKey;
  } catch (error) {
    console.error('Error decrypting wallet:', error);
    return null;
  }
};

// Generate recovery phrase (mnemonic)
const generateRecoveryPhrase = (): string => {
  const wallet = ethers.Wallet.createRandom();
  return wallet.mnemonic.phrase;
};

// Restore wallet from recovery phrase
const restoreWalletFromPhrase = (phrase: string): WalletData | null => {
  try {
    const wallet = ethers.Wallet.fromMnemonic(phrase);
    
    return {
      address: wallet.address,
      privateKey: wallet.privateKey,
      balance: '0.0',
      network: DEFAULT_NETWORK,
      hasBackup: true,
      tokenBalances: {}
    };
  } catch (error) {
    console.error('Error restoring wallet from phrase:', error);
    return null;
  }
};

// Get Solana connection based on network - using Helius API with improved error handling
const getSolanaConnection = (network = DEFAULT_SOLANA_NETWORK) => {
  const rpcUrl = solanaNetworks[network]?.rpcUrl || solanaNetworks['mainnet-beta'].rpcUrl;
  console.log(`Using Solana connection with RPC URL: ${rpcUrl}`);
  
  try {
    // Create connection with retry options
    const connection = new web3.Connection(rpcUrl, { 
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 60000
    });
    
    // Add custom error handling to detect API key issues
    const originalRpcRequest = connection.rpcRequest;
    connection.rpcRequest = async function(method, params) {
      try {
        return await originalRpcRequest.call(connection, method, params);
      } catch (error: any) {
        // Check if it's an API key error
        if (error?.message?.includes('401') || error?.message?.includes('403') || 
            error?.message?.includes('api key not found')) {
          console.error(`API key error with ${rpcUrl}. Falling back to public RPC.`);
          
          // Fallback to a public endpoint
          const fallbackUrl = 'https://api.mainnet-beta.solana.com';
          console.log(`Falling back to ${fallbackUrl}`);
          const fallbackConnection = new web3.Connection(fallbackUrl);
          
          // Try again with the fallback
          return fallbackConnection.rpcRequest(method, params);
        }
        
        // Re-throw other errors
        throw error;
      }
    };
    
    return connection;
  } catch (error) {
    console.error('Error creating Solana connection:', error);
    // Fallback to another endpoint if Helius fails
    const fallbackUrl = 'https://api.mainnet-beta.solana.com';
    console.log(`Falling back to ${fallbackUrl}`);
    return new web3.Connection(fallbackUrl);
  }
};

// Get Solana wallet balance using Helius
const getSolanaWalletBalance = async (walletData: WalletData): Promise<string> => {
  if (!walletData.solana || !walletData.solana.address) {
    return '0.0';
  }

  try {
    const network = walletData.solana.network || DEFAULT_SOLANA_NETWORK;
    const connection = getSolanaConnection(network);
    const publicKey = new web3.PublicKey(walletData.solana.address);
    
    console.log(`Fetching SOL balance for ${walletData.solana.address} using ${solanaNetworks[network].rpcUrl}`);
    
    // Implement retry mechanism for Solana balance fetching
    let retries = 0;
    const maxRetries = 3;
    let lastError;
    
    while (retries <= maxRetries) {
      try {
        const balance = await connection.getBalance(publicKey);
        // Convert lamports to SOL (1 SOL = 1,000,000,000 lamports)
        const solBalance = balance / 1_000_000_000;
        console.log(`Real SOL balance for ${walletData.solana.address}: ${solBalance} SOL`);
        
        return solBalance.toString();
      } catch (error: any) {
        lastError = error;
        console.error(`Error fetching SOL balance (attempt ${retries + 1}/${maxRetries + 1}):`, error);
        
        // Try with a different RPC URL if it's an API key issue
        if (error?.message?.includes('401') || error?.message?.includes('403') || 
            error?.message?.includes('api key not found')) {
          // Try a different RPC URL
          const fallbackUrl = ['https://api.mainnet-beta.solana.com', 'https://solana-api.projectserum.com'][retries % 2];
          console.log(`Trying with alternative URL: ${fallbackUrl}`);
          const fallbackConnection = new web3.Connection(fallbackUrl);
          try {
            const balance = await fallbackConnection.getBalance(publicKey);
            const solBalance = balance / 1_000_000_000;
            console.log(`Real SOL balance (from fallback): ${solBalance} SOL`);
            return solBalance.toString();
          } catch (fallbackError) {
            console.error('Fallback also failed:', fallbackError);
          }
        }
        
        // Wait before retry with exponential backoff
        if (retries < maxRetries) {
          const delay = 1000 * Math.pow(2, retries);
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        retries++;
      }
    }
    
    console.error('All retries failed for SOL balance fetch');
    // If all retries fail, return the cached balance or 0
    return walletData.solana.balance || '0.0';
    
  } catch (error) {
    console.error('Error getting Solana wallet balance:', error);
    return walletData.solana.balance || '0.0';
  }
};

// Get Solana token balance (like USDC) using Helius
const getSolanaTokenBalance = async (
  walletData: WalletData,
  tokenAddress: string
): Promise<string> => {
  if (!walletData.solana || !walletData.solana.address) {
    return '0.0';
  }
  
  try {
    const network = walletData.solana.network || DEFAULT_SOLANA_NETWORK;
    const connection = getSolanaConnection(network);
    const publicKey = new web3.PublicKey(walletData.solana.address);
    
    console.log(`Fetching token ${tokenAddress} balance for ${walletData.solana.address}`);
    
    // Implement retry mechanism
    let retries = 0;
    const maxRetries = 3;
    let lastError;
    
    while (retries <= maxRetries) {
      try {
        // Get token accounts owned by the wallet
        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(publicKey, {
          programId: new web3.PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') // Solana Token Program
        });
        
        // Find the account for the token we're looking for
        const tokenAccount = tokenAccounts.value.find(acc => 
          acc.account.data.parsed.info.mint === tokenAddress
        );
        
        if (tokenAccount) {
          const balance = tokenAccount.account.data.parsed.info.tokenAmount.uiAmount;
          console.log(`Real Solana ${tokenAddress} balance for ${walletData.solana.address}: ${balance} USDC`);
          return balance.toString();
        }
        
        return '0.0';
      } catch (error) {
        lastError = error;
        console.error(`Error fetching token balance (attempt ${retries + 1}/${maxRetries + 1}):`, error);
        
        if (retries < maxRetries) {
          const delay = 1000 * Math.pow(2, retries);
          console.log(`Waiting ${delay}ms before retry...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
        
        retries++;
      }
    }
    
    console.error('All retries failed for token balance fetch');
    return '0.0';
    
  } catch (error) {
    console.error('Error getting Solana token balance:', error);
    return '0.0';
  }
};

// Send SOL
const sendSol = async (
  senderWallet: WalletData,
  recipientAddress: string,
  amount: string,
  message: string = ''
): Promise<TransactionData> => {
  if (!senderWallet.solana || !senderWallet.solana.secretKey) {
    throw new Error('Solana wallet not available');
  }
  
  try {
    console.log(`Sending ${amount} SOL from ${senderWallet.solana.address} to ${recipientAddress}`);
    
    const connection = getSolanaConnection(senderWallet.solana.network);
    const senderPublicKey = new web3.PublicKey(senderWallet.solana.address);
    const recipientPublicKey = new web3.PublicKey(recipientAddress);
    
    // Convert amount from SOL to lamports
    const lamports = Math.floor(parseFloat(amount) * 1_000_000_000);
    
    // Create a transfer instruction
    const transaction = new web3.Transaction().add(
      web3.SystemProgram.transfer({
        fromPubkey: senderPublicKey,
        toPubkey: recipientPublicKey,
        lamports
      })
    );
    
    // Convert base64 secret key to Uint8Array for signing
    const secretKeyBytes = Buffer.from(senderWallet.solana.secretKey!, 'base64');
    const keypair = web3.Keypair.fromSecretKey(secretKeyBytes);
    
    // Get recent blockhash
    const { blockhash } = await connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = senderPublicKey;
    
    // Sign and send the transaction
    const signedTransaction = await web3.sendAndConfirmTransaction(
      connection,
      transaction,
      [keypair]
    );
    
    console.log('Solana transaction sent:', signedTransaction);
    
    // Create transaction data
    const transactionData: TransactionData = {
      id: `tx-sol-${Date.now()}`,
      from: senderWallet.solana.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'completed',
      txHash: signedTransaction,
      type: 'tip',
      message,
      blockchain: 'solana'
    };
    
    return transactionData;
  } catch (error) {
    console.error('Error sending SOL:', error);
    
    // Create failed transaction data
    const failedTx: TransactionData = {
      id: `tx-sol-${Date.now()}`,
      from: senderWallet.solana!.address,
      to: recipientAddress,
      amount,
      timestamp: new Date(),
      status: 'failed',
      type: 'tip',
      message,
      blockchain: 'solana'
    };
    
    return failedTx;
  }
};

// Get Solana transaction URL for block explorer
const getSolanaTransactionUrl = (
  txHash: string,
  network = DEFAULT_SOLANA_NETWORK
) => {
  const blockExplorer = solanaNetworks[network]?.blockExplorer || 'https://explorer.solana.com';
  return `${blockExplorer}/tx/${txHash}?cluster=${network}`;
};

// Get Solana address URL for block explorer
const getSolanaAddressUrl = (
  address: string,
  network = DEFAULT_SOLANA_NETWORK
) => {
  const blockExplorer = solanaNetworks[network]?.blockExplorer || 'https://explorer.solana.com';
  return `${blockExplorer}/address/${address}?cluster=${network}`;
};

// Export all wallet service functions and data
export { commonTokens };

export default {
  createWallet,
  createDeterministicWallet,
  createRandomWallet,
  getBalance,
  sendTransaction,
  getWalletBalance,
  sendTip,
  getTransactionUrl,
  getAddressUrl,
  formatAddress,
  getTokenBalance,
  sendToken,
  generateAddressQRData,
  encryptWallet,
  decryptWallet,
  generateRecoveryPhrase,
  restoreWalletFromPhrase,
  commonTokens,
  getSolanaWalletBalance,
  getSolanaTokenBalance,
  sendSol,
  getSolanaTransactionUrl,
  getSolanaAddressUrl,
  solanaNetworks
};
