{"name": "Audra - Voice Web3 Social Platform", "short_name": "<PERSON><PERSON>", "description": "A decentralized voice-first social platform built on Web3 technology", "start_url": "/", "display": "standalone", "background_color": "#0a0a0a", "theme_color": "#0a0a0a", "orientation": "portrait-primary", "scope": "/", "lang": "en", "categories": ["social", "entertainment", "productivity"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/mobile-home.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Home screen showing voice posts"}, {"src": "/screenshots/mobile-record.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Voice recording interface"}, {"src": "/screenshots/desktop-home.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop view of Audra platform"}], "shortcuts": [{"name": "Record Voice Post", "short_name": "Record", "description": "Quickly record a new voice post", "url": "/?action=record", "icons": [{"src": "/icons/shortcut-record.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Voice Journals", "short_name": "Journals", "description": "Access your voice journals", "url": "/journals", "icons": [{"src": "/icons/shortcut-journal.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Notifications", "short_name": "Notifications", "description": "Check your notifications", "url": "/notifications", "icons": [{"src": "/icons/shortcut-notifications.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}