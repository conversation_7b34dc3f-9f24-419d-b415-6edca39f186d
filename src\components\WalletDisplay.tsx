import React, { useState, useEffect, useRef } from 'react';
import { useWallet } from '@/contexts/WalletContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/sonner';
import { Copy, ExternalLink, RefreshCw, Wallet, ArrowUpRight, ArrowDownLeft, Clock, Check } from 'lucide-react';
import walletService, { TransactionData } from '@/services/walletService';

const WalletDisplay: React.FC = () => {
  const { wallet, transactions, isLoading, refreshBalance, refreshSolanaBalance, balance, solanaBalance } = useWallet();
  const [activeTab, setActiveTab] = useState('overview');
  const [blockchain, setBlockchain] = useState('ethereum');
  const [copied, setCopied] = useState(false);
  const [solCopied, setSolCopied] = useState(false);
  
  // Enhanced balance caching to prevent flickering
  const [localBalance, setLocalBalance] = useState<string | null>(null);
  const [localSolanaBalance, setLocalSolanaBalance] = useState<string | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refreshAttemptsRef = useRef<number>(0);
  const maxRefreshAttempts = 3;
  
  // Copy ETH address to clipboard
  const copyAddress = () => {
    if (!wallet) return;
    
    navigator.clipboard.writeText(wallet.address)
      .then(() => {
        setCopied(true);
        toast.success('ETH address copied to clipboard');
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(() => toast.error('Failed to copy address'));
  };
  
  // Copy SOL address to clipboard
  const copySolAddress = () => {
    if (!wallet || !wallet.solana) return;
    
    navigator.clipboard.writeText(wallet.solana.address)
      .then(() => {
        setSolCopied(true);
        toast.success('SOL address copied to clipboard');
        setTimeout(() => setSolCopied(false), 2000);
      })
      .catch(() => toast.error('Failed to copy address'));
  };
  
  // Open ETH address in block explorer
  const openInExplorer = () => {
    if (!wallet) return;
    
    const url = walletService.getAddressUrl(wallet.address, wallet.network);
    window.open(url, '_blank');
  };
  
  // Open SOL address in block explorer
  const openInSolExplorer = () => {
    if (!wallet || !wallet.solana) return;
    
    const url = walletService.getSolanaAddressUrl(wallet.solana.address, wallet.solana.network);
    window.open(url, '_blank');
  };
  
  // Handle ETH refresh with debounce to prevent flickering
  const handleEthRefresh = () => {
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    
    // Save existing balance to display while refreshing
    if (balance) {
      setLocalBalance(balance);
    }
    
    // Reset attempts counter
    refreshAttemptsRef.current = 0;
    
    // Set a timeout to prevent rapid refresh calls
    refreshTimeoutRef.current = setTimeout(() => {
      refreshBalance();
    }, 500);
  };
  
  // Handle Solana refresh with debounce to prevent flickering
  const handleSolanaRefresh = () => {
    // Clear any existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    
    // Save existing balance to display while refreshing
    if (solanaBalance) {
      setLocalSolanaBalance(solanaBalance);
    }
    
    // Reset attempts counter
    refreshAttemptsRef.current = 0;
    
    // Set a timeout to prevent rapid refresh calls
    refreshTimeoutRef.current = setTimeout(() => {
      refreshSolanaBalance();
    }, 500);
  };
  
  // Effect to refresh balance on component mount
  useEffect(() => {
    if (wallet) {
      // Cache current balance before refreshing
      if (balance) setLocalBalance(balance);
      
      handleEthRefresh();
      
      if (wallet.solana && wallet.solana.address) {
        // Cache current Solana balance before refreshing
        if (solanaBalance) setLocalSolanaBalance(solanaBalance);
        handleSolanaRefresh();
      }
    }
    
    // Cleanup timeout
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [wallet, refreshBalance]);
  
  // Update localBalance whenever official balance changes
  useEffect(() => {
    if (balance) {
      setLocalBalance(balance);
    }
  }, [balance]);
  
  // Update localSolanaBalance whenever official Solana balance changes
  useEffect(() => {
    if (solanaBalance) {
      setLocalSolanaBalance(solanaBalance);
    }
  }, [solanaBalance]);
  
  if (!wallet) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Wallet</CardTitle>
          <CardDescription>You don't have a wallet yet</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground mb-4">
            A wallet will be created for you automatically during registration.
          </p>
        </CardContent>
      </Card>
    );
  }
  
  // Display balance values - use local cache during refresh to prevent flickering
  const displayBalance = isLoading ? localBalance || '0.0' : (balance || '0.0');
  const displaySolanaBalance = isLoading ? localSolanaBalance || '0.0' : (solanaBalance || '0.0');
  
  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleString();
  };
  
  // Get transaction icon based on type
  const getTransactionIcon = (transaction: TransactionData) => {
    if (transaction.type === 'tip') {
      return transaction.from === wallet?.address || (wallet?.solana && transaction.from === wallet.solana.address) ? 
        <ArrowUpRight className="h-4 w-4 text-red-500" /> : 
        <ArrowDownLeft className="h-4 w-4 text-green-500" />;
    }
    return <Clock className="h-4 w-4 text-gray-500" />;
  };
  
  // Get transaction status color
  const getStatusColor = (status: 'pending' | 'completed' | 'failed') => {
    switch (status) {
      case 'pending': return 'text-yellow-500';
      case 'completed': return 'text-green-500';
      case 'failed': return 'text-red-500';
      default: return '';
    }
  };
  
  // Open transaction in block explorer
  const openTransactionInExplorer = (transaction: TransactionData) => {
    if (!transaction.txHash || !wallet) return;
    
    if (transaction.blockchain === 'solana' && wallet.solana) {
      const url = walletService.getSolanaTransactionUrl(transaction.txHash, wallet.solana.network);
      window.open(url, '_blank');
    } else {
      const url = walletService.getTransactionUrl(transaction.txHash, wallet.network);
      window.open(url, '_blank');
    }
  };
  
  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Wallet className="h-5 w-5" />
              <span>Your Wallet</span>
            </CardTitle>
            <CardDescription>Manage your wallet and transactions</CardDescription>
          </div>
          <div className="space-x-2">
            <Button 
              variant="outline" 
              size="icon" 
              onClick={handleEthRefresh}
              disabled={isLoading && refreshAttemptsRef.current < maxRefreshAttempts}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-4 pt-4">
            {/* Blockchain selector */}
            <div className="space-y-2">
              <Label>Select Blockchain</Label>
              <div className="flex space-x-2">
                <Button 
                  variant={blockchain === 'ethereum' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('ethereum')}
                  className="flex-1"
                >
                  <img src="/images/chains/ethereum.png" alt="ETH" className="w-4 h-4 mr-2" />
                  Ethereum (Base)
                </Button>
                <Button 
                  variant={blockchain === 'solana' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('solana')}
                  className="flex-1"
                >
                  <img src="/images/chains/solana.png" alt="SOL" className="w-4 h-4 mr-2" />
                  Solana
                </Button>
              </div>
            </div>
            
            {/* Ethereum wallet section */}
            {blockchain === 'ethereum' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>ETH Address</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      value={wallet.address} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button variant="outline" size="icon" onClick={copyAddress}>
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                    <Button variant="outline" size="icon" onClick={openInExplorer}>
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>ETH Balance</Label>
                  {isLoading && !displayBalance ? (
                    <Skeleton className="h-10 w-full" />
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-secondary rounded-md">
                      <span className="text-xl font-bold">{displayBalance}</span>
                      <span className="text-sm text-muted-foreground">ETH</span>
                    </div>
                  )}
                </div>
                
                {/* Show USDC Balance */}
                <div className="space-y-2">
                  <Label>USDC Balance</Label>
                  {isLoading && !wallet.tokenBalances?.USDC ? (
                    <Skeleton className="h-10 w-full" />
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-secondary rounded-md">
                      <span className="text-xl font-bold">{wallet.tokenBalances?.USDC || '0.0'}</span>
                      <span className="text-sm text-muted-foreground">USDC</span>
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label>Network</Label>
                  <div className="p-3 bg-secondary rounded-md">
                    <span className="text-sm">
                      {wallet.network === 'base' ? 'Base Mainnet' : 'Base Goerli Testnet'}
                    </span>
                  </div>
                </div>
              </div>
            )}
            
            {/* Solana wallet section */}
            {blockchain === 'solana' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Solana Address</Label>
                  <div className="flex items-center gap-2">
                    <Input 
                      value={wallet.solana?.address || 'Loading...'} 
                      readOnly 
                      className="font-mono text-sm"
                    />
                    <Button variant="outline" size="icon" onClick={copySolAddress}>
                      {solCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                    <Button variant="outline" size="icon" onClick={openInSolExplorer}>
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>SOL Balance</Label>
                  {isLoading && !displaySolanaBalance ? (
                    <Skeleton className="h-10 w-full" />
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-secondary rounded-md">
                      <span className="text-xl font-bold">{displaySolanaBalance}</span>
                      <span className="text-sm text-muted-foreground">SOL</span>
                    </div>
                  )}
                </div>
                
                {/* Show Solana USDC Balance */}
                {wallet.solana?.tokenBalances?.USDC !== undefined && (
                  <div className="space-y-2">
                    <Label>USDC Balance (Solana)</Label>
                    {isLoading && !wallet.solana?.tokenBalances?.USDC ? (
                      <Skeleton className="h-10 w-full" />
                    ) : (
                      <div className="flex items-center justify-between p-3 bg-secondary rounded-md">
                        <span className="text-xl font-bold">{wallet.solana.tokenBalances.USDC || '0.0'}</span>
                        <span className="text-sm text-muted-foreground">USDC</span>
                      </div>
                    )}
                  </div>
                )}
                
                <div className="space-y-2">
                  <Label>Network</Label>
                  <div className="p-3 bg-secondary rounded-md">
                    <span className="text-sm">
                      Solana {wallet.solana?.network || 'mainnet-beta'}
                    </span>
                  </div>
                </div>
                
                <div className="mt-2">
                  <Button
                    variant="secondary"
                    className="w-full"
                    onClick={handleSolanaRefresh}
                    disabled={isLoading && refreshAttemptsRef.current >= maxRefreshAttempts}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh Solana Balance
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="transactions" className="pt-4">
            <div className="space-y-2 mb-4">
              <Label>Filter by Blockchain</Label>
              <div className="flex space-x-2">
                <Button 
                  variant={blockchain === 'all' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('all')}
                  className="flex-1"
                >
                  All Chains
                </Button>
                <Button 
                  variant={blockchain === 'ethereum' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('ethereum')}
                  className="flex-1"
                >
                  <img src="/images/chains/ethereum.png" alt="ETH" className="w-4 h-4 mr-2" />
                  ETH
                </Button>
                <Button 
                  variant={blockchain === 'solana' ? 'default' : 'outline'} 
                  onClick={() => setBlockchain('solana')}
                  className="flex-1"
                >
                  <img src="/images/chains/solana.png" alt="SOL" className="w-4 h-4 mr-2" />
                  SOL
                </Button>
              </div>
            </div>
            
            {transactions.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No transactions yet</p>
              </div>
            ) : (
              <div className="space-y-3">
                {transactions
                  .filter(tx => {
                    if (blockchain === 'all') return true;
                    if (blockchain === 'ethereum') return !tx.blockchain || tx.blockchain === 'ethereum';
                    if (blockchain === 'solana') return tx.blockchain === 'solana';
                    return true;
                  })
                  .map((tx) => (
                    <div 
                      key={tx.id} 
                      className="flex items-center justify-between p-3 bg-secondary/50 rounded-md hover:bg-secondary cursor-pointer"
                      onClick={() => openTransactionInExplorer(tx)}
                    >
                      <div className="flex items-center gap-3">
                        {getTransactionIcon(tx)}
                        <div>
                          <p className="text-sm font-medium">
                            {tx.type === 'tip' ? 
                              ((tx.from === wallet.address || (wallet.solana && tx.from === wallet.solana.address)) ? 'Sent Tip' : 'Received Tip') : 
                              tx.type.charAt(0).toUpperCase() + tx.type.slice(1)}
                            {tx.blockchain === 'solana' && <span className="ml-1 text-xs">(SOL)</span>}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {formatDate(tx.timestamp)}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${(tx.from === wallet.address || (wallet.solana && tx.from === wallet.solana.address)) ? 'text-red-500' : 'text-green-500'}`}>
                          {(tx.from === wallet.address || (wallet.solana && tx.from === wallet.solana.address)) ? '-' : '+'}
                          {tx.amount} {tx.token ? tx.token.symbol : (tx.blockchain === 'solana' ? 'SOL' : 'ETH')}
                        </p>
                        <p className={`text-xs ${getStatusColor(tx.status)}`}>
                          {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
                          {tx.txHash && <span className="ml-1 text-xs">(#{tx.txHash.substring(0, 6)})</span>}
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <p className="text-xs text-muted-foreground">
          {blockchain === 'solana' && wallet.solana ? 
            `Connected to Solana ${wallet.solana.network}` : 
            `Connected to ${wallet.network === 'base' ? 'Base Mainnet' : 'Base Goerli Testnet'}`}
        </p>
      </CardFooter>
    </Card>
  );
};

export default WalletDisplay;
