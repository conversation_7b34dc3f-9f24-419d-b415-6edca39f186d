
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { VoiceMessageProps } from '@/types/voice-message';
import VoiceMessageListWithoutChannel from '@/components/VoiceMessageListWithoutChannel';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { StoriesRing } from '@/components/StoriesRing';
import { SpacesList } from '@/components/SpacesList';
import { SpaceViewer } from '@/components/SpaceViewer';
import { LiveSpacesSection } from '@/components/LiveSpacesSection';

interface IndexProps {
  connectedAccount: string;
  messages: VoiceMessageProps[];
  onReply: (parentId: string) => void;
}

const IndexWithoutChannel: React.FC<IndexProps> = ({ connectedAccount, messages, onReply }) => {
  const [activeTab, setActiveTab] = useState('all');
  const [userPosts, setUserPosts] = useState<VoiceMessageProps[]>([]);
  const [selectedSpaceId, setSelectedSpaceId] = useState<string | null>(null);
  const { profiles } = useProfiles();
  const { user } = useAuth();

  // Make sure messages is always an array
  const safeMessages = Array.isArray(messages) ? messages : [];
  
  // Get the user profile from the profiles object
  const userProfile = connectedAccount ?
    profiles[connectedAccount.toLowerCase()] : undefined;

  // Effect to fetch user's posts directly from Supabase
  useEffect(() => {
    if (connectedAccount) {
      console.log('Fetching posts for connected account:', connectedAccount);
      
      const fetchUserPosts = async () => {
        try {
          // First get the profile ID for the connected wallet
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('id')
            .eq('wallet_address', connectedAccount.toLowerCase())
            .single();
            
          if (profileError && profileError.code !== 'PGRST116') {
            console.error('Error fetching profile:', profileError);
            return;
          }
          
          if (profileData) {
            console.log('Found profile for wallet address:', profileData.id);
            
            // Fetch all posts by this profile
            const { data, error } = await supabase
              .from('voice_messages')
              .select('*')
              .eq('profile_id', profileData.id)
              .is('deleted_at', null)
              .order('created_at', { ascending: false });
              
            if (error) {
              console.error('Error fetching user posts:', error);
              return;
            }
            
            if (data && data.length > 0) {
              console.log(`Found ${data.length} posts for user ${profileData.id} in Supabase`);
              
              // Process any posts with string timestamps
              const processedPosts = data.map(post => ({
                id: post.id,
                userAddress: post.profile_id,
                timestamp: new Date(post.created_at),
                transcript: post.transcript || '',
                audioUrl: post.audio_url || '',
                duration: post.audio_duration || 0,
                replies: [],
                isPinned: post.is_pinned || false,
                media: post.media || []
              }));
              
              setUserPosts(processedPosts);
            } else {
              console.log('No posts found for user in Supabase');
              setUserPosts([]);
            }
          } else {
            console.log('No profile found for wallet address:', connectedAccount);
            setUserPosts([]);
          }
        } catch (err) {
          console.error('Error in fetchUserPosts:', err);
        }
      };
      
      fetchUserPosts();
    }
  }, [connectedAccount]);

  // Effect to update userPosts when new messages are added
  useEffect(() => {
    if (connectedAccount && safeMessages.length > 0) {
      // Filter messages to get user's posts
      const userMessages = safeMessages.filter(message => {
        return message.userAddress &&
               message.userAddress.toLowerCase() === connectedAccount.toLowerCase();
      });

      // Check if we have new user posts that aren't in userPosts state
      const currentUserPostIds = new Set(userPosts.map(post => post.id));
      const newUserPosts = userMessages.filter(message => !currentUserPostIds.has(message.id));

      if (newUserPosts.length > 0) {
        console.log(`Found ${newUserPosts.length} new user posts, updating userPosts state`);

        // Merge new posts with existing user posts, sort by timestamp (newest first)
        const updatedUserPosts = [...userPosts, ...newUserPosts]
          .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

        setUserPosts(updatedUserPosts);
      }
    }
  }, [connectedAccount, safeMessages, userPosts]);

  // Filter messages based on active tab
  const getFilteredMessages = () => {
    console.log(`Filtering messages for tab: ${activeTab}`);
    return activeTab === 'all' ? safeMessages : userPosts;
  };

  const filteredMessages = getFilteredMessages();
  console.log(`Filtered messages for ${activeTab} tab:`, filteredMessages.length);

  return (
    <div className="w-full">
      {/* Stories Ring */}
      <div className="mb-6">
        <StoriesRing currentUserId={connectedAccount || 'demo-user'} />
      </div>

      {/* Live Spaces Section */}
      <div className="mb-6">
        <LiveSpacesSection
          currentUserId={connectedAccount || 'demo-user'}
          onJoinSpace={(spaceId) => setSelectedSpaceId(spaceId)}
        />
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">All Posts</TabsTrigger>
          <TabsTrigger value="mine">My Posts</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          {filteredMessages.length > 0 ? (
            <VoiceMessageListWithoutChannel
              messages={filteredMessages}
              onReply={onReply}
              connectedAccount={connectedAccount}
            />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">No voice messages yet. Be the first to share!</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="mine" className="space-y-4">
          {userPosts.length > 0 ? (
            <VoiceMessageListWithoutChannel
              messages={userPosts}
              onReply={onReply}
              connectedAccount={connectedAccount}
            />
          ) : (
            <div className="text-center py-10">
              <p className="text-muted-foreground">You haven't posted any voice messages yet.</p>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Space Viewer Modal */}
      {selectedSpaceId && connectedAccount && (
        <SpaceViewer
          spaceId={selectedSpaceId}
          isOpen={!!selectedSpaceId}
          onClose={() => setSelectedSpaceId(null)}
          currentUserId={connectedAccount}
        />
      )}
    </div>
  );
};

export default IndexWithoutChannel;
