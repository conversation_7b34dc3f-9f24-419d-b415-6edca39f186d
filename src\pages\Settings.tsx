import React, { useState, useEffect } from 'react';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useSettings } from '@/contexts/SettingsContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useChainVoice } from '@/contexts/ChainVoiceContext';
import { toast } from '@/components/ui/sonner';
import { useIsMobile } from '@/hooks/use-mobile';
import { elevenLabsService } from '@/services/elevenLabsService';
import { heliusService } from '@/services/heliusService';
import { apiKeyManager, ApiKeyType } from '@/services/apiKeyManager';
import { Mic } from 'lucide-react';
import VerificationApplicationModal from '@/components/VerificationApplicationModal';
import ProfileDebugger from '@/components/ProfileDebugger';
import JournalDebugger from '@/components/JournalDebugger';
import {
  Bell,
  Moon,
  Sun,
  Monitor,
  Lock,
  Volume2,
  Accessibility,
  Globe,
  Wallet,
  RotateCcw,
  Check,
  Languages,
  Mail,
  MessageSquare,
  Heart,
  DollarSign,
  UserPlus,
  Users,
  Megaphone,
  Eye,
  AtSign,
  Activity,
  Search,
  Play,
  Sliders,
  ZoomIn,
  Sparkles,
  Gauge,
  Wallet2,
  AlertCircle,
  Flame,
  Key,
  Shield,
  HardDrive,
  Database,
  CloudCog
} from 'lucide-react';
import StorageMigrationTool from '@/components/StorageMigrationTool';
import StorageSettings from '@/components/StorageSettings';

interface SettingsProps {
  userAddress?: string;
}

const Settings: React.FC<SettingsProps> = ({ userAddress = '' }) => {
  const {
    notifications,
    privacy,
    audio,
    accessibility,
    language,
    wallet,
    updateNotificationSettings,
    updatePrivacySettings,
    updateAudioSettings,
    updateAccessibilitySettings,
    updateLanguageSettings,
    updateWalletSettings,
    resetSettings
  } = useSettings();

  const { theme, setTheme } = useTheme();
  const { startService } = useChainVoice();
  const [confirmReset, setConfirmReset] = useState(false);
  const [activeSection, setActiveSection] = useState<string>('api-keys');
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [useBlockchainStorage, setUseBlockchainStorage] = useState(
    localStorage.getItem('useBlockchainStorage') === 'true'
  );
  const isMobile = useIsMobile();

  // API key states
  const [elevenLabsApiKey, setElevenLabsApiKey] = useState(apiKeyManager.getApiKey(ApiKeyType.ELEVEN_LABS));
  const [heliusApiKey, setHeliusApiKey] = useState('4418d794-039b-4530-a9c4-6f8e325faa18');
  const [isTestingElevenLabs, setIsTestingElevenLabs] = useState(false);
  const [isTestingHelius, setIsTestingHelius] = useState(false);
  const [elevenLabsVoices, setElevenLabsVoices] = useState<any[]>([]);
  const [selectedVoice, setSelectedVoice] = useState('');

  // Initialize API keys on component mount
  useEffect(() => {
    const initializeApiKeys = async () => {
      // Test ElevenLabs API key if available
      if (elevenLabsApiKey) {
        setIsTestingElevenLabs(true);
        try {
          // Test the API key by fetching voices
          const isValid = await apiKeyManager.validateApiKey(ApiKeyType.ELEVEN_LABS, elevenLabsApiKey);

          if (isValid) {
            // Set the API key in the manager
            apiKeyManager.setApiKey(ApiKeyType.ELEVEN_LABS, elevenLabsApiKey);

            // Fetch voices
            const voices = await elevenLabsService.getVoices();
            setElevenLabsVoices(voices);

            if (voices.length > 0) {
              console.log('ElevenLabs API key is valid!');

              // Set default voice to Antoni if available
              const defaultVoiceId = 'EXAVITQu4vr4xnSDxMaL'; // Antoni voice
              const hasDefaultVoice = voices.some(voice => voice.voice_id === defaultVoiceId);

              if (hasDefaultVoice) {
                setSelectedVoice(defaultVoiceId);
                elevenLabsService.setDefaultVoiceId(defaultVoiceId);
              } else if (voices.length > 0) {
                // Use the first available voice if Antoni is not available
                setSelectedVoice(voices[0].voice_id);
                elevenLabsService.setDefaultVoiceId(voices[0].voice_id);
              }
            }
          }
        } catch (error) {
          console.error('Error testing ElevenLabs API key:', error);
        } finally {
          setIsTestingElevenLabs(false);
        }
      }

      // Test Helius API key if available
      if (heliusApiKey) {
        setIsTestingHelius(true);
        try {
          // Set the API key in the manager first
          apiKeyManager.setApiKey(ApiKeyType.HELIUS, heliusApiKey);

          // Test the API key
          const isValid = await apiKeyManager.validateApiKey(ApiKeyType.HELIUS, heliusApiKey);

          if (isValid) {
            console.log('Helius API key is valid!');
            toast.success('Helius API key is valid!', { id: 'helius-valid' });
          } else {
            console.warn('Helius API key validation failed');
            toast.error('Helius API key validation failed. The app will use mock data.', { id: 'helius-invalid' });
          }
        } catch (error) {
          console.error('Error testing Helius API key:', error);
          toast.error('Error testing Helius API key', { id: 'helius-error' });
        } finally {
          setIsTestingHelius(false);
        }
      }

      // Start the chain voice service with whatever API keys we have
      startService();
    };

    initializeApiKeys();
  }, [elevenLabsApiKey, heliusApiKey, startService, toast]);

  const handleResetSettings = () => {
    if (!confirmReset) {
      setConfirmReset(true);
      return;
    }

    resetSettings();
    setConfirmReset(false);
    toast('Settings have been reset to defaults');
  };

  const sections = [
    { id: 'appearance', label: 'Appearance', icon: Sun },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Lock },
    { id: 'audio', label: 'Audio', icon: Volume2 },
    { id: 'accessibility', label: 'Accessibility', icon: Accessibility },
    { id: 'language', label: 'Language', icon: Globe },
    { id: 'wallet', label: 'Wallet', icon: Wallet },
    { id: 'storage', label: 'Storage', icon: HardDrive },
    { id: 'storage-migration', label: 'Storage Migration', icon: CloudCog },
    { id: 'verification', label: 'Verification', icon: Shield },
    { id: 'api-keys', label: 'API Keys', icon: Key },
    { id: 'admin', label: 'Admin', icon: Shield }
  ];

  return (
    <div className="container mx-auto flex-1 px-0 md:px-4 py-0 md:py-6 max-w-3xl">
      <div className="sticky top-[60px] bg-background/95 backdrop-blur-md py-2 z-10 px-4">
        <h1 className="text-2xl font-bold mb-2">Settings</h1>
        <Separator className="bg-border/50" />
      </div>

      <div className="flex flex-col md:flex-row mt-4 gap-6">
        {/* Sidebar for navigation */}
        <div className="md:w-64 px-4 md:px-0">
          <div className="flex md:flex-col gap-2 overflow-x-auto md:overflow-visible pb-2 md:pb-0 scrollbar-hide">
            {sections.map(section => (
              <Button
                key={section.id}
                variant={activeSection === section.id ? "default" : "ghost"}
                className={`flex items-center justify-start gap-2 ${activeSection === section.id
                  ? "bg-voicechain-purple hover:bg-voicechain-accent"
                  : ""
                  } ${isMobile ? "flex-col py-2 px-3 h-auto min-w-[70px]" : "w-full"}`}
                onClick={() => setActiveSection(section.id)}
              >
                <section.icon size={isMobile ? 20 : 16} />
                <span className={isMobile ? "text-xs" : ""}>{section.label}</span>
              </Button>
            ))}
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 px-4 md:px-0 pb-20 md:pb-0">
          {activeSection === 'appearance' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Theme</h2>
              <div className="flex flex-col space-y-6">
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    className={`relative h-24 ${theme === 'light' ? 'ring-2 ring-voicechain-purple' : ''}`}
                    onClick={() => setTheme('light')}
                  >
                    <div className="absolute inset-0 bg-white flex items-center justify-center">
                      <Sun size={32} className="text-black" />
                    </div>
                    {theme === 'light' && (
                      <div className="absolute bottom-2 right-2 w-4 h-4 bg-voicechain-purple rounded-full flex items-center justify-center">
                        <Check size={12} className="text-white" />
                      </div>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    className={`relative h-24 ${theme === 'dark' ? 'ring-2 ring-voicechain-purple' : ''}`}
                    onClick={() => setTheme('dark')}
                  >
                    <div className="absolute inset-0 bg-[#1A1F2C] flex items-center justify-center">
                      <Moon size={32} className="text-white" />
                    </div>
                    {theme === 'dark' && (
                      <div className="absolute bottom-2 right-2 w-4 h-4 bg-voicechain-purple rounded-full flex items-center justify-center">
                        <Check size={12} className="text-white" />
                      </div>
                    )}
                  </Button>

                  <Button
                    variant="outline"
                    className={`relative h-24 ${theme === 'system' ? 'ring-2 ring-voicechain-purple' : ''}`}
                    onClick={() => setTheme('system')}
                  >
                    <div className="absolute inset-0 bg-gradient-to-b from-white to-[#1A1F2C] flex items-center justify-center">
                      <Monitor size={32} className="text-gray-400" />
                    </div>
                    {theme === 'system' && (
                      <div className="absolute bottom-2 right-2 w-4 h-4 bg-voicechain-purple rounded-full flex items-center justify-center">
                        <Check size={12} className="text-white" />
                      </div>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'notifications' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Notification Preferences</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <AtSign size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="mentions" className="font-medium">Mentions</Label>
                  </div>
                  <Switch
                    id="mentions"
                    checked={notifications.mentions}
                    onCheckedChange={(checked) => updateNotificationSettings({ mentions: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <MessageSquare size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="replies" className="font-medium">Replies</Label>
                  </div>
                  <Switch
                    id="replies"
                    checked={notifications.replies}
                    onCheckedChange={(checked) => updateNotificationSettings({ replies: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Heart size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="likes" className="font-medium">Reactions</Label>
                  </div>
                  <Switch
                    id="likes"
                    checked={notifications.likes}
                    onCheckedChange={(checked) => updateNotificationSettings({ likes: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <DollarSign size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="tips" className="font-medium">Tips</Label>
                  </div>
                  <Switch
                    id="tips"
                    checked={notifications.tips}
                    onCheckedChange={(checked) => updateNotificationSettings({ tips: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>

              <h3 className="text-lg font-medium mt-6">Delivery Methods</h3>
              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Mail size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="emailNotifications" className="font-medium">Email Notifications</Label>
                      <p className="text-xs text-muted-foreground">Receive notifications via email</p>
                    </div>
                  </div>
                  <Switch
                    id="emailNotifications"
                    checked={notifications.emailNotifications}
                    onCheckedChange={(checked) => updateNotificationSettings({ emailNotifications: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Bell size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="pushNotifications" className="font-medium">Push Notifications</Label>
                      <p className="text-xs text-muted-foreground">Receive notifications on your device</p>
                    </div>
                  </div>
                  <Switch
                    id="pushNotifications"
                    checked={notifications.pushNotifications}
                    onCheckedChange={(checked) => updateNotificationSettings({ pushNotifications: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'privacy' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Privacy Settings</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Activity size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="showOnlineStatus" className="font-medium">Online Status</Label>
                      <p className="text-xs text-muted-foreground">Show when you're active on the platform</p>
                    </div>
                  </div>
                  <Switch
                    id="showOnlineStatus"
                    checked={privacy.showOnlineStatus}
                    onCheckedChange={(checked) => updatePrivacySettings({ showOnlineStatus: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <MessageSquare size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="allowDirectMessages" className="font-medium">Direct Messages</Label>
                      <p className="text-xs text-muted-foreground">Allow others to send you direct messages</p>
                    </div>
                  </div>
                  <Switch
                    id="allowDirectMessages"
                    checked={privacy.allowDirectMessages}
                    onCheckedChange={(checked) => updatePrivacySettings({ allowDirectMessages: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <AtSign size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="allowMentions" className="font-medium">Mentions</Label>
                      <p className="text-xs text-muted-foreground">Allow others to mention you in messages</p>
                    </div>
                  </div>
                  <Switch
                    id="allowMentions"
                    checked={privacy.allowMentions}
                    onCheckedChange={(checked) => updatePrivacySettings({ allowMentions: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Volume2 size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="showListeningActivity" className="font-medium">Listening Activity</Label>
                      <p className="text-xs text-muted-foreground">Show others what voice messages you're listening to</p>
                    </div>
                  </div>
                  <Switch
                    id="showListeningActivity"
                    checked={privacy.showListeningActivity}
                    onCheckedChange={(checked) => updatePrivacySettings({ showListeningActivity: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'audio' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Audio Settings</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Play size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="autoPlayVoiceMessages" className="font-medium">Auto-Play Voice Messages</Label>
                      <p className="text-xs text-muted-foreground">Automatically play voice messages when viewing</p>
                    </div>
                  </div>
                  <Switch
                    id="autoPlayVoiceMessages"
                    checked={audio.autoPlayVoiceMessages}
                    onCheckedChange={(checked) => updateAudioSettings({ autoPlayVoiceMessages: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Sliders size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="highQualityAudio" className="font-medium">High Quality Audio</Label>
                      <p className="text-xs text-muted-foreground">Stream audio in higher quality (uses more data)</p>
                    </div>
                  </div>
                  <Switch
                    id="highQualityAudio"
                    checked={audio.highQualityAudio}
                    onCheckedChange={(checked) => updateAudioSettings({ highQualityAudio: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Mic size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="reduceBackground" className="font-medium">Reduce Background Noise</Label>
                      <p className="text-xs text-muted-foreground">Filter out background noise in recordings</p>
                    </div>
                  </div>
                  <Switch
                    id="reduceBackground"
                    checked={audio.reduceBackground}
                    onCheckedChange={(checked) => updateAudioSettings({ reduceBackground: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>

              <h3 className="text-lg font-medium mt-6">Playback Settings</h3>
              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Gauge size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="defaultPlaybackSpeed" className="font-medium">Default Playback Speed</Label>
                  </div>
                  <div className="flex items-center gap-4">
                    <Slider
                      id="defaultPlaybackSpeed"
                      min={0.5}
                      max={2}
                      step={0.25}
                      value={[audio.defaultPlaybackSpeed]}
                      onValueChange={([value]) => updateAudioSettings({ defaultPlaybackSpeed: value })}
                      className="flex-1"
                    />
                    <span className="w-12 text-center font-medium">{audio.defaultPlaybackSpeed}x</span>
                  </div>
                </div>

                <div className="space-y-4 pt-2">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Mic size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="defaultRecordingQuality" className="font-medium">Recording Quality</Label>
                  </div>
                  <Select
                    value={audio.defaultRecordingQuality}
                    onValueChange={(value: 'low' | 'medium' | 'high') =>
                      updateAudioSettings({ defaultRecordingQuality: value })
                    }
                  >
                    <SelectTrigger id="defaultRecordingQuality" className="w-full">
                      <SelectValue placeholder="Select quality" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low (Save data)</SelectItem>
                      <SelectItem value="medium">Medium (Balanced)</SelectItem>
                      <SelectItem value="high">High (Best quality)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'accessibility' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Accessibility</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Activity size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="reduceAnimations" className="font-medium">Reduce Animations</Label>
                      <p className="text-xs text-muted-foreground">Minimize motion effects throughout the app</p>
                    </div>
                  </div>
                  <Switch
                    id="reduceAnimations"
                    checked={accessibility.reduceAnimations}
                    onCheckedChange={(checked) => updateAccessibilitySettings({ reduceAnimations: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Sun size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="highContrastMode" className="font-medium">High Contrast Mode</Label>
                      <p className="text-xs text-muted-foreground">Increase contrast for better visibility</p>
                    </div>
                  </div>
                  <Switch
                    id="highContrastMode"
                    checked={accessibility.highContrastMode}
                    onCheckedChange={(checked) => updateAccessibilitySettings({ highContrastMode: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <ZoomIn size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="largerText" className="font-medium">Larger Text</Label>
                      <p className="text-xs text-muted-foreground">Increase text size throughout the app</p>
                    </div>
                  </div>
                  <Switch
                    id="largerText"
                    checked={accessibility.largerText}
                    onCheckedChange={(checked) => updateAccessibilitySettings({ largerText: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Accessibility size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="screenReaderOptimized" className="font-medium">Screen Reader Optimized</Label>
                      <p className="text-xs text-muted-foreground">Improve compatibility with screen readers</p>
                    </div>
                  </div>
                  <Switch
                    id="screenReaderOptimized"
                    checked={accessibility.screenReaderOptimized}
                    onCheckedChange={(checked) => updateAccessibilitySettings({ screenReaderOptimized: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'language' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Language</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Globe size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="appLanguage" className="font-medium">App Language</Label>
                  </div>
                  <Select
                    value={language.appLanguage}
                    onValueChange={(value) => updateLanguageSettings({ appLanguage: value })}
                  >
                    <SelectTrigger id="appLanguage" className="w-full">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Español</SelectItem>
                      <SelectItem value="fr">Français</SelectItem>
                      <SelectItem value="de">Deutsch</SelectItem>
                      <SelectItem value="ja">日本語</SelectItem>
                      <SelectItem value="zh">中文</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4 pt-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Languages size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="voiceTranscriptionLanguage" className="font-medium">Voice Transcription Language</Label>
                  </div>
                  <Select
                    value={language.voiceTranscriptionLanguage}
                    onValueChange={(value) => updateLanguageSettings({ voiceTranscriptionLanguage: value })}
                  >
                    <SelectTrigger id="voiceTranscriptionLanguage" className="w-full">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="en-GB">English (UK)</SelectItem>
                      <SelectItem value="es-ES">Español</SelectItem>
                      <SelectItem value="fr-FR">Français</SelectItem>
                      <SelectItem value="de-DE">Deutsch</SelectItem>
                      <SelectItem value="ja-JP">日本語</SelectItem>
                      <SelectItem value="zh-CN">中文 (简体)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}



          {activeSection === 'verification' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Verification</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">Apply for Verification</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Verification badges help users identify authentic accounts on Audra.
                    Apply for verification to get a badge on your profile.
                  </p>

                  <Button
                    className="w-full sm:w-auto bg-voicechain-purple hover:bg-voicechain-accent"
                    onClick={() => setIsVerificationModalOpen(true)}
                  >
                    <Shield className="mr-2 h-4 w-4" />
                    Apply for Verification
                  </Button>
                </div>

                <div className="mt-6 pt-4 border-t">
                  <h3 className="text-sm font-medium mb-2">Verification Badge Types</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    <div className="flex items-center gap-2 p-2 rounded-md bg-secondary/30">
                      <div className="bg-purple-900 rounded-full w-4 h-4 flex items-center justify-center">
                        <Check className="text-white w-2.5 h-2.5" />
                      </div>
                      <div>
                        <p className="text-xs font-medium">Purple Badge</p>
                        <p className="text-xs text-muted-foreground">Special verification</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 p-2 rounded-md bg-secondary/30">
                      <div className="bg-blue-500 rounded-full w-4 h-4 flex items-center justify-center">
                        <Check className="text-white w-2.5 h-2.5" />
                      </div>
                      <div>
                        <p className="text-xs font-medium">Content Creator</p>
                        <p className="text-xs text-muted-foreground">Verified creator</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 p-2 rounded-md bg-secondary/30">
                      <div className="bg-green-500 rounded-full w-4 h-4 flex items-center justify-center">
                        <Check className="text-white w-2.5 h-2.5" />
                      </div>
                      <div>
                        <p className="text-xs font-medium">Developer</p>
                        <p className="text-xs text-muted-foreground">Blockchain developer</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2 p-2 rounded-md bg-secondary/30">
                      <div className="bg-orange-500 rounded-full w-4 h-4 flex items-center justify-center">
                        <Check className="text-white w-2.5 h-2.5" />
                      </div>
                      <div>
                        <p className="text-xs font-medium">Partner</p>
                        <p className="text-xs text-muted-foreground">Official partner</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'storage' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Storage Settings</h2>

              {/* Use our new StorageSettings component */}
              <StorageSettings
                onClose={() => {
                  // Update the local state to match the localStorage value
                  const useBlockchain = localStorage.getItem('useBlockchainStorage') !== 'false';
                  setUseBlockchainStorage(useBlockchain);
                }}
              />

              {userAddress && useBlockchainStorage && (
                <div className="space-y-4 rounded-lg border p-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-3">
                      <div className="bg-secondary/80 p-2 rounded-full">
                        <CloudCog size={18} className="text-voicechain-purple" />
                      </div>
                      <h3 className="text-lg font-medium">Storage Migration</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-4">
                      Migrate your existing voice messages from Supabase to decentralized blockchain storage.
                      This process will upload your content to IPFS via Thirdweb.
                    </p>

                    <Button
                      className="w-full sm:w-auto bg-voicechain-purple hover:bg-voicechain-accent"
                      onClick={() => {
                        setActiveSection('storage-migration');
                      }}
                    >
                      <HardDrive className="mr-2 h-4 w-4" />
                      Migrate to Blockchain Storage
                    </Button>
                  </div>
                </div>
              )}

              <div className="rounded-lg border p-4 bg-secondary/20">
                <div className="flex items-start gap-3">
                  <div className="bg-secondary/80 p-2 rounded-full mt-0.5">
                    <AlertCircle size={18} className="text-amber-500" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">About Blockchain Storage</h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      Blockchain storage uses Thirdweb Storage to store your files on IPFS.
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      Thirdweb Storage provides permanent, decentralized storage that cannot be censored or taken down.
                      Your content will be available as long as the IPFS network exists.
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      <strong>Note:</strong> Thirdweb Storage is already configured and ready to use.
                      No additional setup is required.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'storage-migration' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Storage Migration</h2>
              <p className="text-muted-foreground">
                Migrate your voice messages from Supabase to decentralized blockchain storage.
                This process will upload your content to IPFS via Thirdweb.
              </p>

              {userAddress ? (
                <StorageMigrationTool userId={userAddress} />
              ) : (
                <div className="rounded-lg border p-4 bg-amber-500/10">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                    <div>
                      <h3 className="font-medium">User ID Required</h3>
                      <p className="text-sm text-muted-foreground mt-1">
                        You need to be logged in to migrate your voice messages to blockchain storage.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <div className="rounded-lg border p-4 bg-secondary/20">
                <div className="flex items-start gap-3">
                  <div className="bg-secondary/80 p-2 rounded-full mt-0.5">
                    <AlertCircle size={18} className="text-amber-500" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium">About Storage Migration</h3>
                    <p className="text-xs text-muted-foreground mt-1">
                      This tool will migrate your existing voice messages from Supabase to decentralized blockchain storage.
                      The migration process uploads your audio files to IPFS via Thirdweb.
                    </p>
                    <p className="text-xs text-muted-foreground mt-2">
                      <strong>Note:</strong> Migration may take some time depending on the number and size of your voice messages.
                      No wallet connection is required for this process.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeSection === 'wallet' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Wallet Settings</h2>

              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <DollarSign size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="defaultTipAmount" className="font-medium">Default Tip Amount (ETH)</Label>
                  </div>
                  <Input
                    id="defaultTipAmount"
                    type="number"
                    step="0.001"
                    min="0.001"
                    value={wallet.defaultTipAmount}
                    onChange={(e) => updateWalletSettings({ defaultTipAmount: e.target.value })}
                    className="w-full"
                  />
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Wallet2 size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="showBalanceInHeader" className="font-medium">Show Balance in Header</Label>
                      <p className="text-xs text-muted-foreground">Display your wallet balance in the app header</p>
                    </div>
                  </div>
                  <Switch
                    id="showBalanceInHeader"
                    checked={wallet.showBalanceInHeader}
                    onCheckedChange={(checked) => updateWalletSettings({ showBalanceInHeader: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <AlertCircle size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="confirmBeforeTipping" className="font-medium">Confirm Before Tipping</Label>
                      <p className="text-xs text-muted-foreground">Show confirmation dialog before sending tips</p>
                    </div>
                  </div>
                  <Switch
                    id="confirmBeforeTipping"
                    checked={wallet.confirmBeforeTipping}
                    onCheckedChange={(checked) => updateWalletSettings({ confirmBeforeTipping: checked })}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>

                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Wallet size={18} className="text-voicechain-purple" />
                    </div>
                    <div>
                      <Label htmlFor="autoConnectWallet" className="font-medium">Auto-Connect Wallet</Label>
                      <p className="text-xs text-muted-foreground">Automatically connect to MetaMask on startup</p>
                    </div>
                  </div>
                  <Switch
                    id="autoConnectWallet"
                    checked={localStorage.getItem('autoConnectWallet') === 'true'}
                    onCheckedChange={(checked) => {
                      localStorage.setItem('autoConnectWallet', checked.toString());
                      toast.success(checked ? 'Wallet will auto-connect on startup' : 'Auto-connect disabled');
                    }}
                    className="data-[state=checked]:bg-voicechain-purple"
                  />
                </div>
              </div>

              <h3 className="text-lg font-medium mt-6">Gas Settings</h3>
              <div className="space-y-4 rounded-lg border p-4">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="bg-secondary/80 p-2 rounded-full">
                      <Flame size={18} className="text-voicechain-purple" />
                    </div>
                    <Label htmlFor="defaultGasSettings" className="font-medium">Default Gas Settings</Label>
                  </div>
                  <Select
                    value={wallet.defaultGasSettings}
                    onValueChange={(value: 'low' | 'medium' | 'high' | 'custom') =>
                      updateWalletSettings({ defaultGasSettings: value })
                    }
                  >
                    <SelectTrigger id="defaultGasSettings" className="w-full">
                      <SelectValue placeholder="Select gas setting" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low (Slower, cheaper)</SelectItem>
                      <SelectItem value="medium">Medium (Balanced)</SelectItem>
                      <SelectItem value="high">High (Faster, more expensive)</SelectItem>
                      <SelectItem value="custom">Custom</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}


        </div>
      </div>

      {/* Debug Section */}
      <div className="container mx-auto max-w-3xl p-4 space-y-6">
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Debug Tools</h2>
          <JournalDebugger />
        </div>
      </div>

      <div className="fixed bottom-0 left-0 right-0 md:relative bg-background/95 backdrop-blur-md border-t md:border-0 p-4 md:p-0 md:mt-8 md:pt-4 md:border-t">
        <div className="container mx-auto max-w-3xl">
          <Button
            variant={confirmReset ? "destructive" : "outline"}
            onClick={handleResetSettings}
            className="flex items-center gap-2 w-full md:w-auto"
          >
            {confirmReset ? (
              <>
                <Check size={16} />
                Confirm Reset
              </>
            ) : (
              <>
                <RotateCcw size={16} />
                Reset All Settings
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Verification Application Modal */}
      <VerificationApplicationModal
        isOpen={isVerificationModalOpen}
        onClose={() => setIsVerificationModalOpen(false)}
        userAddress={userAddress}
      />
    </div>
  );
};

export default Settings;
