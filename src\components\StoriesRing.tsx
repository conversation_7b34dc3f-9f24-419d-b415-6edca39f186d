import React, { useState, useEffect } from 'react';
import { VoiceStory, storyService } from '@/services/storyService';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { StoryViewer } from './StoryViewer';
import { StoryCreationModal } from './StoryCreationModal';

interface StoriesRingProps {
  currentUserId?: string;
}

export const StoriesRing: React.FC<StoriesRingProps> = ({ currentUserId }) => {
  const [stories, setStories] = useState<VoiceStory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedStoryIndex, setSelectedStoryIndex] = useState<number | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadStories();
  }, [currentUserId]);

  const loadStories = async () => {
    try {
      setIsLoading(true);
      console.log('🔄 StoriesRing: Loading stories for user:', currentUserId);
      const activeStories = await storyService.getActiveStories(currentUserId);
      console.log('📚 StoriesRing: Loaded stories:', activeStories);
      setStories(activeStories);
    } catch (error) {
      console.error('❌ StoriesRing: Error loading stories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStoryClick = (index: number) => {
    setSelectedStoryIndex(index);
  };

  const handleCloseViewer = () => {
    setSelectedStoryIndex(null);
  };

  const handleStoryCreated = () => {
    loadStories(); // Refresh stories after creation
  };

  if (isLoading) {
    return (
      <div className="flex gap-4 p-4 overflow-x-auto">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex-shrink-0">
            <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse" />
            <div className="w-12 h-3 bg-gray-200 rounded mt-2 mx-auto animate-pulse" />
          </div>
        ))}
      </div>
    );
  }

  console.log('🎭 StoriesRing: Rendering with', stories.length, 'stories, currentUserId:', currentUserId);

  return (
    <>
      <div className="flex gap-4 p-4 overflow-x-auto scrollbar-hide">
        {/* Add Story Button */}
        {currentUserId && (
          <div className="flex-shrink-0 text-center">
            <Button
              onClick={() => setShowCreateModal(true)}
              className="w-16 h-16 rounded-full border-2 border-dashed border-primary/50 bg-background hover:bg-accent p-0"
              variant="ghost"
            >
              <Plus className="h-6 w-6 text-primary" />
            </Button>
            <p className="text-xs text-muted-foreground mt-2 max-w-[64px] truncate">
              Your Story
            </p>
          </div>
        )}

        {/* Stories */}
        {stories.map((story, index) => (
          <div
            key={story.id}
            className="flex-shrink-0 text-center cursor-pointer"
            onClick={() => handleStoryClick(index)}
          >
            <div
              className={`w-16 h-16 rounded-full p-1 ${
                story.has_viewed 
                  ? 'bg-gradient-to-r from-gray-300 to-gray-400' 
                  : 'bg-gradient-to-r from-primary to-secondary'
              }`}
            >
              <div 
                className="w-full h-full rounded-full bg-cover bg-center flex items-center justify-center text-white font-semibold"
                style={{
                  backgroundColor: story.background_color,
                  backgroundImage: story.background_image_url 
                    ? `url(${story.background_image_url})` 
                    : undefined
                }}
              >
                {!story.background_image_url && (
                  <span className="text-sm">
                    {story.profile_id.slice(0, 2).toUpperCase()}
                  </span>
                )}
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-2 max-w-[64px] truncate">
              {story.title || story.profile_id.slice(0, 8)}
            </p>
          </div>
        ))}

        {/* Empty State */}
        {stories.length === 0 && (
          <div className="flex-1 text-center py-8">
            <p className="text-muted-foreground text-sm">
              No stories available. Be the first to share one!
            </p>
          </div>
        )}
      </div>

      {/* Story Viewer */}
      {selectedStoryIndex !== null && (
        <StoryViewer
          stories={stories}
          initialIndex={selectedStoryIndex}
          onClose={handleCloseViewer}
          currentUserId={currentUserId}
        />
      )}

      {/* Story Creation Modal */}
      {currentUserId && (
        <StoryCreationModal
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onStoryCreated={handleStoryCreated}
          profileId={currentUserId}
        />
      )}
    </>
  );
};