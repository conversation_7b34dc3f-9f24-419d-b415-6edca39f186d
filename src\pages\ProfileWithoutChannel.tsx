import React, { useState, useEffect, useCallback } from 'react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import CustomAvatarImage from '@/components/CustomAvatarImage';
import ProfileCoverImage from '@/components/ProfileCoverImage';
import { User, Settings, LogOut, Copy, ExternalLink, MessageCircle, Heart, DollarSign, Edit, Globe, AtSign, Shield, Pin, BookOpen, Play, Calendar, Lock } from 'lucide-react';
import { toast } from '@/components/ui/sonner';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import ProfileEditModal from '@/components/ProfileEditModal';
import VoiceSummonModalWithoutChannel from '@/components/VoiceSummonModalWithoutChannel';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { Badge } from '@/components/ui/badge';
import { VoiceMessageProps } from '@/components/VoiceMessage';
import { MediaFile } from '@/components/MediaUploader';
import VoiceMessageWithoutChannel from '@/components/VoiceMessageWithoutChannel';
import VerificationBadgeV2 from '@/components/VerificationBadgeV2';
import { getVerificationDescription } from '@/utils/verification';
import VerificationApplicationModal from '@/components/VerificationApplicationModal';
import UserProfileActions from '@/components/UserProfileActions';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import FollowButton from '@/components/FollowButton';
import { JournalEntry } from '@/types/journal';
import JournalCard from '@/components/JournalCard';
import { formatDistanceToNow } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import { useLocalStorage } from '@/hooks/use-local-storage';
import { getDefaultProfile } from '@/utils/profile-defaults';
import { forceSyncProfileToSupabase, forceSyncProfileFromSupabase } from '@/utils/profileSyncUtils';

interface ProfileProps {
  userAddress?: string;
}

const ProfileWithoutChannel: React.FC<ProfileProps> = ({ userAddress = '' }) => {
  const { getProfileByAddress, updateProfile, incrementPostCount, refreshProfile } = useProfiles();
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isSummonModalOpen, setIsSummonModalOpen] = useState(false);
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [userPosts, setUserPosts] = useState<VoiceMessageProps[]>([]);
  const [likedPosts, setLikedPosts] = useState<VoiceMessageProps[]>([]);
  const [pinnedPost, setPinnedPost] = useState<VoiceMessageProps | null>(null);
  const [userJournals, setUserJournals] = useState<JournalEntry[]>([]);
  const [profile, setProfile] = useState<UserProfile>(getDefaultProfile());
  const [profileUpdateCounter, setProfileUpdateCounter] = useState(0);
  const { user } = useAuth();
  const navigate = useNavigate();
  const params = useParams();

  // Get the address from URL params if available, otherwise use the prop
  const address = params.address || userAddress;

  console.log('Profile page - address:', address);
  console.log('Profile page - userAddress prop:', userAddress);
  console.log('Profile page - params.address:', params.address);
  console.log('Profile page - current URL:', window.location.href);

  // Get the current user's connected account
  const getCurrentUserAccount = () => {
    // First try to get from auth context
    if (user && user.walletAddress) {
      return user.walletAddress;
    }

    // If all else fails, try to get the wallet address from the URL
    const urlParams = new URLSearchParams(window.location.search);
    const walletParam = urlParams.get('wallet');
    if (walletParam) {
      return walletParam;
    }

    // Last resort - hardcode the owner's address
    // This is a fallback to ensure the delete button shows up for the owner
    return '******************************************';
  };

  // Load profile data
  useEffect(() => {
    const loadProfile = async () => {
      if (!address) {
        console.log('No address provided, using current user');
        return;
      }

      try {
        console.log('Loading profile for address:', address);

        // Try to get the profile from our service
        const storedProfile = getProfileByAddress(address);

        if (storedProfile) {
          console.log('Found profile in storage:', storedProfile);

          // Make sure stats are properly initialized
          const updatedProfile = {
            ...storedProfile,
            stats: {
              posts: storedProfile.stats?.posts || 0,
              followers: storedProfile.stats?.followers || 0,
              following: storedProfile.stats?.following || 0,
              likes: storedProfile.stats?.likes || 0,
              tips: storedProfile.stats?.tips || 0
            }
          };

          setProfile(updatedProfile);
        } else {
          // Fall back to the local profile
          const localProfile = getProfileByAddress(address);
          console.log("Using local profile:", localProfile);

          // Make sure stats are properly initialized
          const updatedProfile = {
            ...localProfile,
            stats: {
              posts: localProfile.stats?.posts || 0,
              followers: localProfile.stats?.followers || 0,
              following: localProfile.stats?.following || 0,
              likes: localProfile.stats?.likes || 0,
              tips: localProfile.stats?.tips || 0
            }
          };

          setProfile(updatedProfile);
        }

        // Load follower/following counts from Supabase using FollowService
        try {
          const { FollowService } = await import('@/services/followService');

          // Get follower count
          const followerCount = await FollowService.getFollowerCount(address);
          console.log(`Found ${followerCount} followers in database`);

          // Get following count
          const followingCount = await FollowService.getFollowingCount(address);
          console.log(`Found ${followingCount} following in database`);

          // Update profile with counts
          setProfile(prev => ({
            ...prev,
            stats: {
              ...prev.stats,
              followers: followerCount,
              following: followingCount
            }
          }));
        } catch (dbError) {
          console.error("Error loading follower/following counts:", dbError);
        }
      } catch (error) {
        console.error("Error loading profile:", error);
        // Fall back to the local profile
        const localProfile = getProfileByAddress(address);
        console.log("Using local profile due to error:", localProfile);

        // Make sure stats are properly initialized
        const updatedProfile = {
          ...localProfile,
          stats: {
            posts: localProfile.stats?.posts || 0,
            followers: localProfile.stats?.followers || 0,
            following: localProfile.stats?.following || 0,
            likes: localProfile.stats?.likes || 0,
            tips: localProfile.stats?.tips || 0
          }
        };

        setProfile(updatedProfile);
      }
    };

    loadProfile();
  }, [address, profileUpdateCounter, getProfileByAddress]);

  // Load user posts and journals
  useEffect(() => {
    const loadUserData = async () => {
      if (!address) return;

      try {
        // Load user posts from Supabase
        try {
          console.log('Loading user posts from Supabase for address:', address);
          console.log('Profile wallet address:', profile.walletAddress);
          console.log('Profile address:', profile.address);

          // Import the database service
          const { getPostsByWalletAddress, getVoiceMessages } = await import('@/services/databaseService');

          // Store the connected account in localStorage for reference
          localStorage.setItem('connectedAccount', address);

          // Get all posts first - this is more reliable for finding posts
          const allPosts = await getVoiceMessages();
          console.log(`Total posts in database: ${allPosts.length}`);

          // Get the actual wallet address to search for
          // The address parameter might be a profile ID, so we need to get the wallet address
          let searchAddress = address;
          let profileId = null;

          if (profile.walletAddress) {
            searchAddress = profile.walletAddress;
            console.log('Using profile wallet address for search:', searchAddress);
          }

          // Get the profile ID for database queries
          try {
            const { data: profileData, error: profileError } = await supabase
              .from('profiles')
              .select('id')
              .ilike('wallet_address', searchAddress)
              .single();

            if (profileData) {
              profileId = profileData.id;
              console.log('Found profile ID:', profileId);
            } else {
              console.log('No profile ID found for address:', searchAddress);
            }
          } catch (error) {
            console.error('Error getting profile ID:', error);
          }

          // Filter posts for this user with multiple matching strategies
          const matchingPosts = allPosts.filter(post => {
            // Check if the post has a userAddress
            if (!post.userAddress) return false;

            // Check if the post's userAddress matches the profile address (case-insensitive)
            const addressMatch = searchAddress &&
              post.userAddress.toLowerCase() === searchAddress.toLowerCase();

            // Check if the post's userAddress matches the original address parameter
            const originalAddressMatch = address &&
              post.userAddress.toLowerCase() === address.toLowerCase();

            // Check if the post's profile_id matches the address
            const profileIdMatch = post.profile_id === address;

            console.log(`Post ${post.id}: userAddress=${post.userAddress}, profile_id=${post.profile_id}, searchAddress=${searchAddress}, addressMatch=${addressMatch}, originalAddressMatch=${originalAddressMatch}, profileIdMatch=${profileIdMatch}`);

            // Return true if any of the conditions match
            return addressMatch || originalAddressMatch || profileIdMatch;
          });

          console.log(`Found ${matchingPosts.length} posts with matching address in all posts`);

          // Get posts using optimized service
          const { getUserVoiceMessagesOptimized } = await import('@/services/optimizedVoiceMessageService');
          const dbPosts = await getUserVoiceMessagesOptimized(searchAddress);
          console.log(`Loaded ${dbPosts.length} posts from Supabase for user ${searchAddress}`);

          // Also try with the original address if different
          let dbPostsOriginal: any[] = [];
          if (searchAddress !== address) {
            dbPostsOriginal = await getPostsByWalletAddress(address);
            console.log(`Loaded ${dbPostsOriginal.length} posts from Supabase for original address ${address}`);
          }

          // Also load from localStorage as a fallback
          const storedMessages = localStorage.getItem('audra_voice_messages') || '[]';
          let localMessages: VoiceMessageProps[] = [];

          try {
            // Parse the saved messages and convert string dates back to Date objects
            const parsedMessages = JSON.parse(storedMessages);

            // Filter messages by user address (try both search address and original address)
            localMessages = parsedMessages
              .filter((msg: any) => {
                if (!msg.userAddress) return false;
                const msgAddress = msg.userAddress.toLowerCase();
                return msgAddress === searchAddress.toLowerCase() || msgAddress === address.toLowerCase();
              })
              .map((msg: any) => ({
                ...msg,
                timestamp: new Date(msg.timestamp),
                // Ensure userAddress is consistent
                userAddress: searchAddress
              }));

            console.log(`Loaded ${localMessages.length} messages from localStorage for user ${address}`);
          } catch (localError) {
            console.error('Error loading saved messages from localStorage:', localError);
          }

          // Merge messages from both sources, prioritizing database messages
          // Create a map of message IDs to avoid duplicates
          const messageMap = new Map<string, VoiceMessageProps>();

          // Add database messages first (they take priority)
          dbPosts.forEach(msg => {
            messageMap.set(msg.id, {
              ...msg,
              // Ensure userAddress is consistent with the search address
              userAddress: searchAddress
            });
          });

          // Add posts from original address query if different
          dbPostsOriginal.forEach(msg => {
            if (!messageMap.has(msg.id)) {
              messageMap.set(msg.id, {
                ...msg,
                // Ensure userAddress is consistent
                userAddress: searchAddress
              });
            }
          });

          // Add matching posts from the full database query if they're not already included
          matchingPosts.forEach(msg => {
            if (!messageMap.has(msg.id)) {
              messageMap.set(msg.id, {
                ...msg,
                // Ensure userAddress is consistent with the search address
                userAddress: searchAddress
              });
            }
          });

          // Add localStorage messages only if they don't exist in the database
          localMessages.forEach(msg => {
            if (!messageMap.has(msg.id)) {
              messageMap.set(msg.id, {
                ...msg,
                // Ensure userAddress is consistent
                userAddress: address
              });
            }
          });

          // Convert map back to array and sort by pinned status first, then timestamp (newest first)
          const mergedMessages = Array.from(messageMap.values())
            .sort((a, b) => {
              // First sort by pinned status (pinned posts first)
              if (a.isPinned && !b.isPinned) return -1;
              if (!a.isPinned && b.isPinned) return 1;

              // Then sort by timestamp (newest first)
              const aTime = typeof a.timestamp === 'number' ? a.timestamp : new Date(a.timestamp).getTime();
              const bTime = typeof b.timestamp === 'number' ? b.timestamp : new Date(b.timestamp).getTime();
              return bTime - aTime;
            });

          console.log(`Total unique messages after merge: ${mergedMessages.length}`);

          // Find pinned post if any
          const pinnedMessage = mergedMessages.find((msg) => msg.isPinned);
          if (pinnedMessage) {
            setPinnedPost(pinnedMessage);
          }

          // Update state with merged messages
          setUserPosts(mergedMessages);

          // Also update localStorage with the merged messages
          localStorage.setItem('audra_voice_messages', JSON.stringify(mergedMessages));

          // Update post count in profile stats
          if (mergedMessages.length !== profile.stats.posts) {
            console.log(`Updating post count from ${profile.stats.posts} to ${mergedMessages.length}`);

            // Update the profile stats immediately
            setProfile(prev => ({
              ...prev,
              stats: {
                ...prev.stats,
                posts: mergedMessages.length
              }
            }));

            // Also update in the context
            incrementPostCount(searchAddress, mergedMessages.length);
          }

          // Load user journals from Supabase (only if we have a profileId)
          if (profileId) {
            try {
              console.log('Loading user journals from Supabase for profile ID:', profileId);

              const { data: journalsData, error: journalsError } = await supabase
                .from('journals')
                .select('*')
                .eq('profile_id', profileId)
                .order('created_at', { ascending: false });

              if (journalsError) {
                console.error('Error fetching user journals:', journalsError);
              } else {
                console.log(`Found ${journalsData?.length || 0} journals for user`);

                // Transform to JournalEntry format
                const formattedJournals = (journalsData || []).map(journal => ({
                  id: journal.id,
                  userAddress: searchAddress,
                  title: journal.title || '',
                  transcript: journal.transcript || '',
                  audioUrl: journal.audio_url || '',
                  duration: journal.audio_duration || 0,
                  createdAt: new Date(journal.created_at),
                  scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
                  isPublished: journal.is_published !== false,
                  isLocked: Boolean(journal.is_locked),
                  unlockCondition: journal.unlock_condition || { type: 'date', value: '' },
                  media: []
                }));

                setUserJournals(formattedJournals);
              }
            } catch (error) {
              console.error('Error loading user journals:', error);
            }

            // Load user liked posts from Supabase
            try {
              console.log('Loading user liked posts from Supabase for profile ID:', profileId);

              const { data: likesData, error: likesError } = await supabase
                .from('likes')
                .select(`
                  id,
                  voice_message_id,
                  created_at,
                  voice_messages (
                    id,
                    audio_url,
                    transcript,
                    created_at,
                    audio_duration,
                    is_pinned,
                    profile_id,
                    profiles (
                      wallet_address,
                      display_name
                    )
                  )
                `)
                .eq('profile_id', profileId)
                .order('created_at', { ascending: false });

              if (likesError) {
                console.error('Error fetching user likes:', likesError);
              } else {
                console.log(`Found ${likesData?.length || 0} liked posts for user`);

                // Transform to VoiceMessageProps format
                const formattedLikedPosts = (likesData || [])
                  .filter(like => like.voice_messages) // Only include likes with valid voice messages
                  .map(like => ({
                    id: like.voice_messages.id,
                    userAddress: like.voice_messages.profiles?.wallet_address || like.voice_messages.profile_id,
                    audioUrl: like.voice_messages.audio_url,
                    transcript: like.voice_messages.transcript || '',
                    timestamp: new Date(like.voice_messages.created_at),
                    duration: Number(like.voice_messages.audio_duration) || 0,
                    isPinned: Boolean(like.voice_messages.is_pinned),
                    media: [],
                    replies: []
                  }));

                setLikedPosts(formattedLikedPosts);
              }
            } catch (error) {
              console.error('Error loading user liked posts:', error);
            }
          } else {
            console.log('No profile ID available, skipping journals and likes loading');
          }
        } catch (error) {
          console.error('Error loading user posts:', error);

          // Fall back to localStorage only if database load fails
          try {
            const storedMessages = localStorage.getItem('audra_voice_messages') || '[]';
            const allMessages = JSON.parse(storedMessages);

            // Filter messages by user address
            const userMessages = allMessages.filter((msg: any) =>
              msg.userAddress && msg.userAddress.toLowerCase() === address.toLowerCase()
            );

            // Sort by timestamp (newest first)
            userMessages.sort((a: any, b: any) => {
              const dateA = new Date(a.timestamp);
              const dateB = new Date(b.timestamp);
              return dateB.getTime() - dateA.getTime();
            });

            // Convert timestamps to Date objects
            const processedMessages = userMessages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));

            console.log("Processed user messages from localStorage fallback:", processedMessages.length);

            // Find pinned post if any
            const pinnedMessage = processedMessages.find((msg: any) => msg.isPinned);
            if (pinnedMessage) {
              setPinnedPost(pinnedMessage);
            }

            setUserPosts(processedMessages);

            // Increment post count if needed
            if (processedMessages.length !== profile.stats.posts) {
              incrementPostCount(address, processedMessages.length);
            }
          } catch (fallbackError) {
            console.error('Error loading user posts from localStorage fallback:', fallbackError);
          }
        }
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    loadUserData();
  }, [address, profile.stats.posts, incrementPostCount, profileUpdateCounter]);

  // Function to refresh all user data (posts, journals, likes)
  const refreshUserData = async () => {
    if (!address) return;

    console.log('Refreshing all user data...');

    try {
      // Get the profile ID
      let searchAddress = address;
      let profileId = null;

      if (profile.walletAddress) {
        searchAddress = profile.walletAddress;
      }

      const { data: profileData } = await supabase
        .from('profiles')
        .select('id')
        .ilike('wallet_address', searchAddress)
        .single();

      if (profileData) {
        profileId = profileData.id;

        // Refresh posts
        const { getPostsByWalletAddress } = await import('@/services/databaseService');
        const dbPosts = await getPostsByWalletAddress(searchAddress);
        setUserPosts(dbPosts);

        // Refresh journals
        const { data: journalsData } = await supabase
          .from('journals')
          .select('*')
          .eq('profile_id', profileId)
          .order('created_at', { ascending: false });

        const formattedJournals = (journalsData || []).map(journal => ({
          id: journal.id,
          userAddress: searchAddress,
          title: journal.title || '',
          transcript: journal.transcript || '',
          audioUrl: journal.audio_url || '',
          duration: journal.audio_duration || 0,
          createdAt: new Date(journal.created_at),
          scheduledFor: journal.scheduled_for ? new Date(journal.scheduled_for) : undefined,
          isPublished: journal.is_published !== false,
          isLocked: Boolean(journal.is_locked),
          unlockCondition: journal.unlock_condition || { type: 'date', value: '' },
          media: []
        }));

        setUserJournals(formattedJournals);

        // Refresh likes
        const { data: likesData } = await supabase
          .from('likes')
          .select(`
            id,
            voice_message_id,
            created_at,
            voice_messages (
              id,
              audio_url,
              transcript,
              created_at,
              audio_duration,
              is_pinned,
              profile_id,
              profiles (
                wallet_address,
                display_name
              )
            )
          `)
          .eq('profile_id', profileId)
          .order('created_at', { ascending: false });

        const formattedLikedPosts = (likesData || [])
          .filter(like => like.voice_messages)
          .map(like => ({
            id: like.voice_messages.id,
            userAddress: like.voice_messages.profiles?.wallet_address || like.voice_messages.profile_id,
            audioUrl: like.voice_messages.audio_url,
            transcript: like.voice_messages.transcript || '',
            timestamp: new Date(like.voice_messages.created_at),
            duration: Number(like.voice_messages.audio_duration) || 0,
            isPinned: Boolean(like.voice_messages.is_pinned),
            media: [],
            replies: []
          }));

        setLikedPosts(formattedLikedPosts);

        // Update post count
        setProfile(prev => ({
          ...prev,
          stats: {
            ...prev.stats,
            posts: dbPosts.length
          }
        }));

        console.log(`Refreshed: ${dbPosts.length} posts, ${formattedJournals.length} journals, ${formattedLikedPosts.length} likes`);
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  // Get the wallet address from the database if available
  const walletAddress = profile.walletAddress || address;
  const formattedAddress = walletAddress ?
    `${walletAddress.substring(0, 6)}...${walletAddress.substring(walletAddress.length - 4)}` :
    'Unknown Address';

  const copyAddress = () => {
    navigator.clipboard.writeText(walletAddress);
    toast('Address copied to clipboard');
  };

  const viewOnExplorer = () => {
    window.open(`https://basescan.org/address/${walletAddress}`, '_blank');
  };

  const disconnect = () => {
    // Clear localStorage
    localStorage.removeItem('connectedAccount');
    localStorage.removeItem('currentProfile');

    // Sign out from Supabase
    supabase.auth.signOut().then(() => {
      toast.success('Disconnected successfully');
      navigate('/login');
    });
  };

  const handleProfileUpdate = async (updatedProfile: UserProfileUpdate) => {
    try {
      // Update the profile
      await updateProfile(address, updatedProfile);

      // Force sync the profile to Supabase
      if (profile) {
        const completeProfile = {
          ...profile,
          ...updatedProfile,
          socialLinks: {
            ...profile.socialLinks,
            ...updatedProfile.socialLinks
          },
          stats: {
            posts: profile.stats?.posts || 0,
            likes: profile.stats?.likes || 0,
            tips: profile.stats?.tips || 0
          }
        };

        await forceSyncProfileToSupabase(completeProfile);
      }

      // Refresh profiles to get the latest data
      await refreshProfiles();

      // Increment the counter to trigger a UI refresh
      setProfileUpdateCounter(prev => prev + 1);

      console.log("Profile updated, force synced to Supabase, refreshed profiles and incremented counter to refresh UI");
      toast.success("Profile updated successfully");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  // Function to force sync profile from Supabase
  const handleForceSync = async () => {
    try {
      toast.loading('Syncing profile...');

      // Force sync from Supabase
      const syncedProfile = await forceSyncProfileFromSupabase(address);

      if (syncedProfile) {
        // Update the local profile state
        setProfile(syncedProfile);

        // Refresh profiles in the context
        await refreshProfiles();

        // Increment the counter to trigger a UI refresh
        setProfileUpdateCounter(prev => prev + 1);

        toast.dismiss();
        toast.success('Profile synced successfully!');
      } else {
        toast.dismiss();
        toast.error('No profile found in Supabase');
      }
    } catch (error) {
      console.error('Error force syncing profile:', error);
      toast.dismiss();
      toast.error('Failed to sync profile');
    }
  };

  const handleSummon = () => {
    const currentAccount = getCurrentUserAccount();
    if (currentAccount === address) {
      toast.error('You cannot summon yourself');
      return;
    }

    setIsSummonModalOpen(true);
  };

  // Handle reply to a post
  const handleReply = (_postId: string) => {
    // In a real implementation, this would open a reply modal
    // For now, we'll just show a toast
    toast.info('Reply functionality coming soon!');
  };

  // Check if the current user is the profile owner
  const isCurrentUser = () => {
    // First try to get from auth context
    const currentAccount = user?.walletAddress || getCurrentUserAccount();
    console.log('Checking if current user:', currentAccount, 'matches profile address:', address);

    // If either is empty, return false
    if (!currentAccount || !address) return false;

    // Compare the addresses (case-insensitive)
    return currentAccount.toLowerCase() === address.toLowerCase();
  };

  return (
    <div className="w-full max-w-4xl mx-auto pb-12">
      {/* Cover Image */}
      <div className="relative h-48 md:h-64 rounded-b-lg overflow-hidden">
        <ProfileCoverImage
          src={profile.coverImageUrl}
          alt={`${profile.displayName}'s cover`}
          className="w-full h-full object-cover"
        />
      </div>

      <div className="px-2 sm:px-4">
        {/* Profile Header */}
        <div className="flex flex-col md:flex-row md:items-end md:justify-between -mt-16 md:-mt-20 mb-6">
          {/* Avatar and Name */}
          <div className="flex flex-col md:flex-row md:items-end gap-4">
            <Avatar className="h-32 w-32 border-4 border-background shadow-md">
              <CustomAvatarImage src={profile.profileImageUrl} alt="Profile" />
              <AvatarFallback className="bg-voicechain-purple text-white text-3xl">
                {profile.displayName.charAt(0)}
              </AvatarFallback>
            </Avatar>

            <div className="mt-4 md:mt-0 md:mb-2">
              <div className="flex items-center gap-2">
                <h2 className="text-2xl font-bold">{profile.displayName}</h2>
                {profile.verification?.isVerified && profile.verification.type && (
                  <VerificationBadgeV2
                    type={profile.verification.type}
                    size="sm"
                  />
                )}
              </div>
              <p className="text-sm text-muted-foreground">@{profile.username}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex mt-4 md:mt-0 gap-2">
            {/* Show Edit button for current user */}
            {isCurrentUser() ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditModalOpen(true)}
              >
                <Edit size={14} className="mr-1" />
                Edit Profile
              </Button>
            ) : (
              /* Show Follow button for other users */
              getCurrentUserAccount() && getCurrentUserAccount().toLowerCase() !== address.toLowerCase() && (
                <div className="flex gap-2">
                  <FollowButton
                    userToFollowAddress={address}
                    currentUserAddress={getCurrentUserAccount()}
                    size="sm"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSummon}
                  >
                    <AtSign size={14} className="mr-1" />
                    Summon
                  </Button>
                </div>
              )
            )}
          </div>
        </div>

        {/* Profile Info */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Bio and Links */}
          <div className="md:col-span-2">
            {profile.bio && (
              <p className="text-sm mb-4">{profile.bio}</p>
            )}

            <div className="flex flex-wrap gap-2 mb-4">
              <Button variant="outline" size="sm" onClick={copyAddress} className="h-8">
                <Copy size={14} className="mr-1" />
                {formattedAddress}
              </Button>
              <Button variant="outline" size="sm" onClick={viewOnExplorer} className="h-8">
                <ExternalLink size={14} />
              </Button>

              {profile.socialLinks?.twitter && (
                <Badge variant="outline" className="flex items-center gap-1 h-8 px-3">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter">
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" />
                  </svg>
                  {profile.socialLinks.twitter}
                </Badge>
              )}
              {profile.socialLinks?.website && (
                <Badge variant="outline" className="flex items-center gap-1 h-8 px-3">
                  <Globe size={14} />
                  {profile.socialLinks.website}
                </Badge>
              )}
            </div>

            {/* Show verification status */}
            {profile.verification?.isVerified && profile.verification.type && (
              <div className="bg-voicechain-purple/10 rounded-lg p-3 flex items-start gap-2">
                <Shield size={16} className="text-voicechain-purple mt-0.5" />
                <div>
                  <p className="text-sm font-medium">
                    {getVerificationDescription(profile.verification.type)}
                  </p>
                  {profile.verification.since && (
                    <p className="text-xs text-muted-foreground">
                      Verified {formatDistanceToNow(profile.verification.since, { addSuffix: true })}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Show verification application button for current user if not verified */}
            {isCurrentUser() && (!profile.verification?.isVerified) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsVerificationModalOpen(true)}
                className="mt-4"
              >
                <Shield size={14} className="mr-1" />
                Apply for Verification
              </Button>
            )}
          </div>

          {/* Stats */}
          <div className="bg-secondary/30 rounded-lg p-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-center cursor-pointer hover:opacity-80" onClick={() => toast.info("Followers list coming soon")}>
                <p className="text-2xl font-semibold">{profile.stats.followers}</p>
                <p className="text-xs text-muted-foreground">Followers</p>
              </div>
              <div className="text-center cursor-pointer hover:opacity-80" onClick={() => toast.info("Following list coming soon")}>
                <p className="text-2xl font-semibold">{profile.stats.following}</p>
                <p className="text-xs text-muted-foreground">Following</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-2">
              <div className="text-center bg-background/50 rounded-md py-2">
                <p className="text-lg font-semibold">{profile.stats.posts}</p>
                <p className="text-xs text-muted-foreground">Posts</p>
              </div>
              <div className="text-center bg-background/50 rounded-md py-2">
                <p className="text-lg font-semibold">{profile.stats.likes}</p>
                <p className="text-xs text-muted-foreground">Reactions</p>
              </div>
              <div className="text-center bg-background/50 rounded-md py-2">
                <p className="text-lg font-semibold">{profile.stats.tips}</p>
                <p className="text-xs text-muted-foreground">Tips</p>
              </div>
            </div>
          </div>
        </div>

        <Separator className="mb-6" />

        <Tabs defaultValue="posts" className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="posts">
              <MessageCircle size={16} className="mr-2" />
              Posts
            </TabsTrigger>
            <TabsTrigger value="journals">
              <BookOpen size={16} className="mr-2" />
              Journals
            </TabsTrigger>
            <TabsTrigger value="likes">
              <Heart size={16} className="mr-2" />
              Reactions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="posts" className="space-y-4">
            {userPosts.length === 0 ? (
              <div className="bg-secondary/50 rounded-lg p-8 flex flex-col items-center justify-center">
                <MessageCircle size={32} className="text-muted-foreground mb-2" />
                <h3 className="font-medium">No posts yet</h3>
                <p className="text-sm text-muted-foreground">Your voice messages will appear here</p>

                {/* Button to refresh all data */}
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={refreshUserData}
                >
                  Refresh Posts
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Posts ({userPosts.length})</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshUserData}
                  >
                    Refresh
                  </Button>
                </div>
                {userPosts.map(post => (
                  <VoiceMessageWithoutChannel
                    key={post.id}
                    id={post.id}
                    audioUrl={post.audioUrl}
                    transcript={post.transcript}
                    userAddress={post.userAddress}
                    timestamp={post.timestamp}
                    duration={post.duration}
                    replies={post.replies || []}
                    media={post.media || []}
                    isPinned={post.isPinned}
                    onReply={handleReply}
                  />
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="journals" className="space-y-4">
            {userJournals.length === 0 ? (
              <div className="bg-secondary/50 rounded-lg p-8 flex flex-col items-center justify-center">
                <BookOpen size={32} className="text-muted-foreground mb-2" />
                <h3 className="font-medium">No journals yet</h3>
                <p className="text-sm text-muted-foreground">Voice journals will appear here</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={refreshUserData}
                >
                  Refresh Journals
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Journals ({userJournals.length})</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshUserData}
                  >
                    Refresh
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {userJournals.map(journal => (
                    <JournalCard
                      key={journal.id}
                      journal={journal}
                      isOwner={isCurrentUser()}
                    />
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="likes" className="space-y-4">
            {likedPosts.length === 0 ? (
              <div className="bg-secondary/50 rounded-lg p-8 flex flex-col items-center justify-center">
                <Heart size={32} className="text-muted-foreground mb-2" />
                <h3 className="font-medium">No liked posts yet</h3>
                <p className="text-sm text-muted-foreground">Posts you like will appear here</p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={refreshUserData}
                >
                  Refresh Reactions
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-semibold">Liked Posts ({likedPosts.length})</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={refreshUserData}
                  >
                    Refresh
                  </Button>
                </div>
                {likedPosts.map(post => (
                  <VoiceMessageWithoutChannel
                    key={post.id}
                    id={post.id}
                    audioUrl={post.audioUrl}
                    transcript={post.transcript}
                    userAddress={post.userAddress}
                    timestamp={post.timestamp}
                    duration={post.duration}
                    isPinned={post.isPinned}
                    media={post.media}
                    replies={post.replies}
                  />
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {isCurrentUser() && (
          <div className="flex gap-2 mt-8">
            <Button
              variant="outline"
              size="sm"
              onClick={handleForceSync}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2"><path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"></path><path d="M3 3v5h5"></path><path d="M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"></path><path d="M16 21h5v-5"></path></svg>
              Sync Profile
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={disconnect}
            >
              <LogOut size={14} className="mr-2" />
              Disconnect
            </Button>
          </div>
        )}
      </div>

      {/* Profile Edit Modal */}
      <ProfileEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        profile={profile}
        onSave={handleProfileUpdate}
      />

      {/* Voice Summon Modal */}
      <VoiceSummonModalWithoutChannel
        isOpen={isSummonModalOpen}
        onClose={() => setIsSummonModalOpen(false)}
        recipientAddress={address}
        context="profile"
      />

      {/* Verification Application Modal */}
      <VerificationApplicationModal
        isOpen={isVerificationModalOpen}
        onClose={() => setIsVerificationModalOpen(false)}
        userAddress={address}
      />
    </div>
  );
};

export default ProfileWithoutChannel;
