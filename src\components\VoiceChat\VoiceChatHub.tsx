import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MessageCircle,
  Users,
  Plus,
  Search,
  Mic,
  Volume2,
  Heart,
  Zap,
  Sparkles,
  Waves,
  Brain,
  Palette,
  Globe,
  Lock,
  Crown,
  Star,
  Headphones,
  Send,
  Smile,
  Paperclip,
  DollarSign,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';

interface VoiceChat {
  id: string;
  name: string;
  type: 'direct' | 'group' | 'space' | 'wave';
  participants: string[];
  lastMessage?: {
    content: string;
    timestamp: string;
    sender: string;
    type: 'voice' | 'text' | 'emotion' | 'reaction';
  };
  unreadCount: number;
  emotionalTone: 'calm' | 'excited' | 'focused' | 'creative' | 'mysterious';
  isOnline?: boolean;
  hasVoiceActivity?: boolean;
  spatialPosition?: { x: number; y: number; z: number };
}

interface EmotionalWave {
  id: string;
  emotion: string;
  intensity: number;
  color: string;
  timestamp: string;
}

const VoiceChatHub: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('chats');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [emotionalWaves, setEmotionalWaves] = useState<EmotionalWave[]>([]);

  // Mock data - replace with real data
  const [chats, setChats] = useState<VoiceChat[]>([
    {
      id: '1',
      name: 'Alice Cooper',
      type: 'direct',
      participants: ['alice', 'me'],
      lastMessage: {
        content: 'Hey! Check out this new track I made 🎵',
        timestamp: '2 min ago',
        sender: 'alice',
        type: 'voice'
      },
      unreadCount: 2,
      emotionalTone: 'excited',
      isOnline: true,
      hasVoiceActivity: true
    },
    {
      id: '2',
      name: 'Creative Minds',
      type: 'group',
      participants: ['bob', 'charlie', 'diana', 'me'],
      lastMessage: {
        content: 'The AI suggestions are incredible!',
        timestamp: '5 min ago',
        sender: 'bob',
        type: 'text'
      },
      unreadCount: 0,
      emotionalTone: 'creative',
      hasVoiceActivity: false
    },
    {
      id: '3',
      name: 'Crypto Waves',
      type: 'wave',
      participants: ['eve', 'frank', 'grace', 'me'],
      lastMessage: {
        content: '💎 Just sent 0.1 ETH tip!',
        timestamp: '10 min ago',
        sender: 'eve',
        type: 'emotion'
      },
      unreadCount: 1,
      emotionalTone: 'mysterious',
      hasVoiceActivity: true
    }
  ]);

  const getEmotionalColor = (tone: string) => {
    const colors = {
      calm: 'bg-blue-500/20 border-blue-500/50',
      excited: 'bg-orange-500/20 border-orange-500/50',
      focused: 'bg-green-500/20 border-green-500/50',
      creative: 'bg-purple-500/20 border-purple-500/50',
      mysterious: 'bg-indigo-500/20 border-indigo-500/50'
    };
    return colors[tone as keyof typeof colors] || colors.calm;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'direct': return <MessageCircle className="h-4 w-4" />;
      case 'group': return <Users className="h-4 w-4" />;
      case 'space': return <Globe className="h-4 w-4" />;
      case 'wave': return <Waves className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const createNewChat = (type: 'direct' | 'group' | 'wave') => {
    // TODO: Implement chat creation modal
    toast.success(`Creating new ${type} chat...`);
  };

  const startVoiceMessage = () => {
    setIsRecording(!isRecording);
    if (!isRecording) {
      toast.success('🎤 Voice recording started');
    } else {
      toast.success('🎵 Voice message sent');
    }
  };

  const sendEmotionalWave = (emotion: string) => {
    const wave: EmotionalWave = {
      id: Date.now().toString(),
      emotion,
      intensity: Math.random() * 100,
      color: `hsl(${Math.random() * 360}, 70%, 60%)`,
      timestamp: new Date().toISOString()
    };
    setEmotionalWaves(prev => [...prev, wave]);
    toast.success(`${emotion} wave sent! 🌊`);
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <MessageCircle className="h-8 w-8 text-primary" />
              <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />
            </div>
            <div>
              <h1 className="text-xl font-bold">VoiceWave Chat</h1>
              <p className="text-sm text-muted-foreground">Revolutionary messaging</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <Brain className="h-4 w-4 mr-2" />
              AI Assistant
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 border-r bg-card flex flex-col">
          {/* Search */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search chats or summon users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 m-4 mb-2">
              <TabsTrigger value="chats" className="text-xs">
                <MessageCircle className="h-3 w-3 mr-1" />
                Chats
              </TabsTrigger>
              <TabsTrigger value="waves" className="text-xs">
                <Waves className="h-3 w-3 mr-1" />
                Waves
              </TabsTrigger>
              <TabsTrigger value="spaces" className="text-xs">
                <Globe className="h-3 w-3 mr-1" />
                Spaces
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chats" className="flex-1 m-0">
              <ScrollArea className="flex-1">
                <div className="p-2 space-y-1">
                  {/* Create New Buttons */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('direct')}
                      className="h-8"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Direct
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('group')}
                      className="h-8"
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Group
                    </Button>
                  </div>

                  {/* Chat List */}
                  {chats.map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotionalTone)}`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback>
                                {chat.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            {chat.isOnline && (
                              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                            )}
                            {chat.hasVoiceActivity && (
                              <Volume2 className="absolute -top-1 -right-1 h-3 w-3 text-blue-500 animate-pulse" />
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {getTypeIcon(chat.type)}
                                <span className="font-medium text-sm truncate">
                                  {chat.name}
                                </span>
                              </div>
                              {chat.unreadCount > 0 && (
                                <Badge variant="destructive" className="h-5 text-xs">
                                  {chat.unreadCount}
                                </Badge>
                              )}
                            </div>

                            {chat.lastMessage && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  {chat.lastMessage.content}
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {chat.lastMessage.timestamp}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    {chat.lastMessage.type === 'voice' && (
                                      <Mic className="h-3 w-3 text-blue-500" />
                                    )}
                                    {chat.lastMessage.type === 'emotion' && (
                                      <Heart className="h-3 w-3 text-red-500" />
                                    )}
                                    <Sparkles className="h-3 w-3 text-yellow-500" />
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="waves" className="flex-1 m-0">
              <div className="p-4 text-center">
                <Waves className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-medium mb-2">Emotional Waves</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Send and receive emotional energy across the network
                </p>
                <Button onClick={() => createNewChat('wave')}>
                  <Zap className="h-4 w-4 mr-2" />
                  Create Wave
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="spaces" className="flex-1 m-0">
              <div className="p-4 text-center">
                <Globe className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="font-medium mb-2">Voice Spaces</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Join spatial audio conversations
                </p>
                <Button>
                  <Headphones className="h-4 w-4 mr-2" />
                  Join Space
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <div className="border-b bg-card p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>AC</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">Alice Cooper</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Online • Excited mood
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <Brain className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Palette className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <DollarSign className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  <div className="text-center text-sm text-muted-foreground">
                    Revolutionary messaging experience coming soon...
                  </div>
                </div>
              </ScrollArea>

              {/* Input Area */}
              <div className="border-t bg-card p-4">
                <div className="flex items-center gap-2">
                  <Button
                    variant={isRecording ? "destructive" : "outline"}
                    size="sm"
                    onClick={startVoiceMessage}
                  >
                    <Mic className="h-4 w-4" />
                  </Button>
                  
                  <Input
                    placeholder="Type a message or speak your mind..."
                    className="flex-1"
                  />
                  
                  <Button variant="ghost" size="sm">
                    <Smile className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Paperclip className="h-4 w-4" />
                  </Button>
                  <Button size="sm">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                {/* Emotional Wave Buttons */}
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-xs text-muted-foreground">Send wave:</span>
                  {['💙', '🔥', '⚡', '🌟', '🌊'].map((emoji, i) => (
                    <Button
                      key={i}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => sendEmotionalWave(emoji)}
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Welcome to VoiceWave Chat</h3>
                <p className="text-muted-foreground mb-4">
                  Select a chat to start your revolutionary messaging experience
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={() => createNewChat('direct')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Start Direct Chat
                  </Button>
                  <Button variant="outline" onClick={() => createNewChat('group')}>
                    <Users className="h-4 w-4 mr-2" />
                    Create Group
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceChatHub;
