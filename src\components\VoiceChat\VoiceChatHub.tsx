import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  MessageCircle,
  Users,
  Plus,
  Search,
  Mic,
  Volume2,
  Heart,
  Zap,
  Sparkles,
  <PERSON>s,
  Brain,
  Palette,
  <PERSON>,
  <PERSON>,
  Crown,
  Star,
  Headphones,
  Send,
  Smile,
  Paperclip,
  DollarSign,
  Settings,
  Play,
  Pause,
  MicOff,
  X
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/components/ui/sonner';
import { voiceChatService, type VoiceChat, type VoiceChatMessage, type EmotionalWave } from '@/services/voiceChatService';
import { voiceRecordingService } from '@/services/voiceRecordingService';
import { supabase } from '@/integrations/supabase/client';
import MessageReactions from '@/components/MessageReactions';
import TipModalWithoutWallet from '@/components/TipModalWithoutWallet';

// Interfaces are now imported from the service

const VoiceChatHub: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('chats');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [emotionalWaves, setEmotionalWaves] = useState<EmotionalWave[]>([]);

  // Real data states
  const [chats, setChats] = useState<VoiceChat[]>([]);
  const [messages, setMessages] = useState<VoiceChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Voice recording states
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [recordingBlob, setRecordingBlob] = useState<Blob | null>(null);
  const [isProcessingVoice, setIsProcessingVoice] = useState(false);

  // Tip modal states
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipRecipientId, setTipRecipientId] = useState<string>('');

  // Image upload states
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showAIModal, setShowAIModal] = useState(false);
  const [showSpaceModal, setShowSpaceModal] = useState(false);
  const [createChatType, setCreateChatType] = useState<'direct' | 'group' | 'wave'>('direct');

  // Create chat form states
  const [chatName, setChatName] = useState('');
  const [chatDescription, setChatDescription] = useState('');
  const [isPrivate, setIsPrivate] = useState(false);
  const [participantEmails, setParticipantEmails] = useState('');

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Load user's chats on component mount
  useEffect(() => {
    if (user?.id) {
      loadUserChats();
    }
  }, [user?.id]);

  // Load messages when chat is selected
  useEffect(() => {
    if (selectedChat) {
      loadChatMessages(selectedChat);
      loadEmotionalWaves(selectedChat);
    }
  }, [selectedChat]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadUserChats = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const userChats = await voiceChatService.getUserChats(user.id);
      setChats(userChats);
    } catch (error) {
      console.error('Error loading chats:', error);
      toast.error('Failed to load chats');
    } finally {
      setIsLoading(false);
    }
  };

  const loadChatMessages = async (chatId: string) => {
    try {
      const chatMessages = await voiceChatService.getChatMessages(chatId);
      setMessages(chatMessages);
    } catch (error) {
      console.error('Error loading messages:', error);
      toast.error('Failed to load messages');
    }
  };

  const loadEmotionalWaves = async (chatId: string) => {
    try {
      const waves = await voiceChatService.getEmotionalWaves(chatId);
      setEmotionalWaves(waves);
    } catch (error) {
      console.error('Error loading emotional waves:', error);
    }
  };

  const getEmotionalColor = (tone: string) => {
    const colors = {
      calm: 'bg-blue-500/20 border-blue-500/50',
      excited: 'bg-orange-500/20 border-orange-500/50',
      focused: 'bg-green-500/20 border-green-500/50',
      creative: 'bg-purple-500/20 border-purple-500/50',
      mysterious: 'bg-indigo-500/20 border-indigo-500/50'
    };
    return colors[tone as keyof typeof colors] || colors.calm;
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'direct': return <MessageCircle className="h-4 w-4" />;
      case 'group': return <Users className="h-4 w-4" />;
      case 'space': return <Globe className="h-4 w-4" />;
      case 'wave': return <Waves className="h-4 w-4" />;
      default: return <MessageCircle className="h-4 w-4" />;
    }
  };

  const createNewChat = (type: 'direct' | 'group' | 'wave') => {
    setCreateChatType(type);
    setShowCreateModal(true);
    setChatName('');
    setChatDescription('');
    setIsPrivate(false);
    setParticipantEmails('');
  };

  const handleCreateChat = async () => {
    if (!user?.id || !chatName.trim()) {
      toast.error('Please enter a chat name');
      return;
    }

    try {
      // Process participants - convert emails to user IDs if needed
      const participantIds = participantEmails
        .split('\n')
        .map(email => email.trim())
        .filter(email => email && email !== user.email);

      console.log('Creating chat:', {
        name: chatName.trim(),
        type: createChatType,
        creator_id: user.id,
        participants: participantIds
      });

      const chatData = await voiceChatService.createChat({
        name: chatName.trim(),
        description: chatDescription.trim() || undefined,
        type: createChatType,
        creator_id: user.id,
        is_private: isPrivate,
        participants: participantIds,
      });

      if (chatData) {
        toast.success(`${createChatType.charAt(0).toUpperCase() + createChatType.slice(1)} chat "${chatName}" created successfully!`);
        setShowCreateModal(false);
        // Reset form
        setChatName('');
        setChatDescription('');
        setIsPrivate(false);
        setParticipantEmails('');
        // Reload chats and select the new one
        await loadUserChats();
        setSelectedChat(chatData.id);
      } else {
        toast.error('Failed to create chat');
      }
    } catch (error) {
      console.error('Error creating chat:', error);
      toast.error('Failed to create chat');
    }
  };

  const startVoiceMessage = async () => {
    if (!selectedChat || !user?.id) return;

    if (!isRecording) {
      // Start recording
      try {
        const recorder = await voiceRecordingService.startRecording();
        if (recorder) {
          setMediaRecorder(recorder);
          setIsRecording(true);

          const chunks: Blob[] = [];
          recorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
              chunks.push(event.data);
            }
          };

          recorder.onstop = () => {
            const blob = new Blob(chunks, { type: 'audio/webm' });
            setRecordingBlob(blob);
          };

          recorder.start();
          toast.success('🎤 Voice recording started');
        }
      } catch (error) {
        console.error('Error starting recording:', error);
        toast.error('Failed to start recording');
      }
    } else {
      // Stop recording
      if (mediaRecorder) {
        mediaRecorder.stop();
        setIsRecording(false);
        setIsProcessingVoice(true);
        toast.success('🎵 Processing voice message...');
      }
    }
  };

  // Process voice recording when blob is ready
  useEffect(() => {
    if (recordingBlob && selectedChat && user?.id) {
      processVoiceMessage();
    }
  }, [recordingBlob]);

  const processVoiceMessage = async () => {
    if (!recordingBlob || !selectedChat || !user?.id) return;

    try {
      const voiceData = await voiceRecordingService.processVoiceRecording(
        recordingBlob,
        user.id,
        selectedChat
      );

      if (voiceData) {
        const message = await voiceChatService.sendVoiceMessage(
          selectedChat,
          user.id,
          voiceData.url,
          voiceData.duration,
          voiceData.transcript
        );

        if (message) {
          setMessages(prev => [...prev, message]);
          toast.success('🎵 Voice message sent!');
        } else {
          toast.error('Failed to send voice message');
        }
      } else {
        toast.error('Failed to process voice recording');
      }
    } catch (error) {
      console.error('Error processing voice message:', error);
      toast.error('Failed to send voice message');
    } finally {
      setIsProcessingVoice(false);
      setRecordingBlob(null);
    }
  };

  const sendTextMessage = async () => {
    if (!selectedChat || !user?.id || !newMessage.trim()) return;

    try {
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        newMessage.trim()
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setNewMessage('');
      } else {
        toast.error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendTextMessage();
    }
  };

  const sendEmotionalWave = async (emotion: string) => {
    if (!selectedChat || !user?.id) return;

    try {
      const wave = await voiceChatService.sendEmotionalWave(
        selectedChat,
        user.id,
        emotion,
        Math.random() * 100
      );

      if (wave) {
        setEmotionalWaves(prev => [...prev, wave]);
        toast.success(`${emotion} wave sent! 🌊`);

        // Update chat emotional tone based on wave
        await updateChatEmotionalTone(selectedChat, emotion);

        // Reload messages to show the wave message
        await loadChatMessages(selectedChat);
        await loadUserChats(); // Reload to get updated emotional tone
      } else {
        toast.error('Failed to send emotional wave');
      }
    } catch (error) {
      console.error('Error sending emotional wave:', error);
      toast.error('Failed to send emotional wave');
    }
  };

  const updateChatEmotionalTone = async (chatId: string, emotion: string) => {
    // Map emotions to tones
    const emotionToTone: { [key: string]: string } = {
      '💙': 'calm',
      '🔥': 'excited',
      '⚡': 'focused',
      '🌟': 'creative',
      '🌊': 'mysterious',
    };

    const newTone = emotionToTone[emotion] || 'calm';

    try {
      await supabase
        .from('voice_chats')
        .update({
          emotional_tone: newTone,
          updated_at: new Date().toISOString()
        })
        .eq('id', chatId);
    } catch (error) {
      console.error('Error updating emotional tone:', error);
    }
  };

  const changeTheme = () => {
    if (!selectedChat) return;

    const themes = ['calm', 'excited', 'focused', 'creative', 'mysterious'];
    const currentChat = chats.find(c => c.id === selectedChat);
    const currentIndex = themes.indexOf(currentChat?.emotional_tone || 'calm');
    const nextIndex = (currentIndex + 1) % themes.length;
    const newTheme = themes[nextIndex];

    updateChatEmotionalTone(selectedChat, newTheme === 'calm' ? '💙' :
                                        newTheme === 'excited' ? '🔥' :
                                        newTheme === 'focused' ? '⚡' :
                                        newTheme === 'creative' ? '🌟' : '🌊');

    toast.success(`Theme changed to ${newTheme}! 🎨`);
  };

  const openTipModal = () => {
    if (!selectedChat) return;

    const currentChat = chats.find(c => c.id === selectedChat);
    if (currentChat) {
      setTipRecipientId(currentChat.creator_id);
      setShowTipModal(true);
    }
  };

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedImage(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const sendImageMessage = async () => {
    if (!selectedImage || !selectedChat || !user?.id) return;

    try {
      // Upload image to Supabase Storage
      const fileName = `chat_image_${Date.now()}_${selectedImage.name}`;
      const { data, error } = await supabase.storage
        .from('voice-recordings')
        .upload(`chat_images/${fileName}`, selectedImage);

      if (error) {
        toast.error('Failed to upload image');
        return;
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('voice-recordings')
        .getPublicUrl(data.path);

      // Send as text message with image URL
      const message = await voiceChatService.sendTextMessage(
        selectedChat,
        user.id,
        `📷 Image: ${urlData.publicUrl}`
      );

      if (message) {
        setMessages(prev => [...prev, message]);
        setSelectedImage(null);
        setImagePreview(null);
        toast.success('Image sent! 📷');
      }
    } catch (error) {
      console.error('Error sending image:', error);
      toast.error('Failed to send image');
    }
  };

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Header */}
      <div className="border-b bg-card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="relative">
              <MessageCircle className="h-8 w-8 text-primary" />
              <Sparkles className="h-4 w-4 text-yellow-500 absolute -top-1 -right-1" />
            </div>
            <div>
              <h1 className="text-xl font-bold">VoiceWave Chat</h1>
              <p className="text-sm text-muted-foreground">Revolutionary messaging</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => setShowSettingsModal(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm" onClick={() => setShowAIModal(true)}>
              <Brain className="h-4 w-4 mr-2" />
              AI Assistant
            </Button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <div className="w-80 border-r bg-card flex flex-col">
          {/* Search */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search chats or summon users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3 m-4 mb-2">
              <TabsTrigger value="chats" className="text-xs">
                <MessageCircle className="h-3 w-3 mr-1" />
                Chats
              </TabsTrigger>
              <TabsTrigger value="waves" className="text-xs">
                <Waves className="h-3 w-3 mr-1" />
                Waves
              </TabsTrigger>
              <TabsTrigger value="spaces" className="text-xs">
                <Globe className="h-3 w-3 mr-1" />
                Spaces
              </TabsTrigger>
            </TabsList>

            <TabsContent value="chats" className="flex-1 m-0">
              <ScrollArea className="flex-1">
                <div className="p-2 space-y-1">
                  {/* Create New Buttons */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('direct')}
                      className="h-8"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Direct
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => createNewChat('group')}
                      className="h-8"
                    >
                      <Users className="h-3 w-3 mr-1" />
                      Group
                    </Button>
                  </div>

                  {/* Chat List */}
                  {chats.map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotionalTone)}`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <Avatar className="h-10 w-10">
                              <AvatarFallback>
                                {chat.name.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            {chat.isOnline && (
                              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                            )}
                            {chat.hasVoiceActivity && (
                              <Volume2 className="absolute -top-1 -right-1 h-3 w-3 text-blue-500 animate-pulse" />
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                {getTypeIcon(chat.type)}
                                <span className="font-medium text-sm truncate">
                                  {chat.name}
                                </span>
                              </div>
                              {chat.unreadCount > 0 && (
                                <Badge variant="destructive" className="h-5 text-xs">
                                  {chat.unreadCount}
                                </Badge>
                              )}
                            </div>

                            {chat.lastMessage && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  {chat.lastMessage.content}
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {chat.lastMessage.timestamp}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    {chat.lastMessage.type === 'voice' && (
                                      <Mic className="h-3 w-3 text-blue-500" />
                                    )}
                                    {chat.lastMessage.type === 'emotion' && (
                                      <Heart className="h-3 w-3 text-red-500" />
                                    )}
                                    <Sparkles className="h-3 w-3 text-yellow-500" />
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="waves" className="flex-1 m-0">
              <ScrollArea className="flex-1">
                <div className="p-2 space-y-1">
                  {/* Create Wave Button */}
                  <div className="p-4 text-center border-b">
                    <Waves className="h-8 w-8 mx-auto text-primary mb-2" />
                    <h3 className="font-medium mb-2">Emotional Waves</h3>
                    <p className="text-xs text-muted-foreground mb-3">
                      Send and receive emotional energy across the network
                    </p>
                    <Button onClick={() => createNewChat('wave')} size="sm" className="w-full">
                      <Zap className="h-4 w-4 mr-2" />
                      Create Wave Chat
                    </Button>
                  </div>

                  {/* Wave Chats */}
                  {chats.filter(chat => chat.type === 'wave').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotional_tone)} border-l-4 border-l-purple-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                              <Waves className="h-5 w-5 text-white" />
                            </div>
                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-purple-500 rounded-full border-2 border-background animate-pulse" />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Waves className="h-4 w-4 text-purple-500" />
                                <span className="font-medium text-sm truncate">
                                  {chat.name}
                                </span>
                              </div>
                              {chat.unread_count && chat.unread_count > 0 && (
                                <Badge variant="destructive" className="h-5 text-xs">
                                  {chat.unread_count}
                                </Badge>
                              )}
                            </div>

                            {chat.last_message && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  {chat.last_message.content}
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(chat.last_message.created_at).toLocaleTimeString()}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Sparkles className="h-3 w-3 text-purple-500" />
                                    <span className="text-xs text-purple-500">Wave Energy</span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {chats.filter(chat => chat.type === 'wave').length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Waves className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No wave chats yet</p>
                      <p className="text-xs">Create one to start sending emotional energy!</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="spaces" className="flex-1 m-0">
              <ScrollArea className="flex-1">
                <div className="p-2 space-y-1">
                  {/* Create/Join Space Buttons */}
                  <div className="p-4 text-center border-b">
                    <Globe className="h-8 w-8 mx-auto text-blue-500 mb-2" />
                    <h3 className="font-medium mb-2">Voice Spaces</h3>
                    <p className="text-xs text-muted-foreground mb-3">
                      Join spatial audio conversations with 3D positioning
                    </p>
                    <div className="flex gap-2">
                      <Button onClick={() => setShowSpaceModal(true)} size="sm" className="flex-1">
                        <Headphones className="h-4 w-4 mr-2" />
                        Join Space
                      </Button>
                      <Button onClick={() => createNewChat('space')} variant="outline" size="sm" className="flex-1">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Space
                      </Button>
                    </div>
                  </div>

                  {/* Space Chats */}
                  {chats.filter(chat => chat.type === 'space').map((chat) => (
                    <Card
                      key={chat.id}
                      className={`cursor-pointer transition-all hover:shadow-md ${
                        selectedChat === chat.id ? 'ring-2 ring-primary' : ''
                      } ${getEmotionalColor(chat.emotional_tone)} border-l-4 border-l-blue-500`}
                      onClick={() => setSelectedChat(chat.id)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-start gap-3">
                          <div className="relative">
                            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                              <Globe className="h-5 w-5 text-white" />
                            </div>
                            <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-background" />
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Globe className="h-4 w-4 text-blue-500" />
                                <span className="font-medium text-sm truncate">
                                  {chat.name}
                                </span>
                                <Badge variant="secondary" className="text-xs">
                                  3D Audio
                                </Badge>
                              </div>
                              {chat.unread_count && chat.unread_count > 0 && (
                                <Badge variant="destructive" className="h-5 text-xs">
                                  {chat.unread_count}
                                </Badge>
                              )}
                            </div>

                            {chat.last_message && (
                              <div className="mt-1">
                                <p className="text-xs text-muted-foreground truncate">
                                  {chat.last_message.content}
                                </p>
                                <div className="flex items-center justify-between mt-1">
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(chat.last_message.created_at).toLocaleTimeString()}
                                  </span>
                                  <div className="flex items-center gap-1">
                                    <Headphones className="h-3 w-3 text-blue-500" />
                                    <span className="text-xs text-blue-500">Spatial Audio</span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {chats.filter(chat => chat.type === 'space').length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Globe className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No voice spaces yet</p>
                      <p className="text-xs">Create or join a space for 3D audio experience!</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>

        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          {selectedChat ? (
            <>
              {/* Chat Header */}
              <div className="border-b bg-card p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback>AC</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">Alice Cooper</h3>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Online • Excited mood
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm" onClick={() => setShowAIModal(true)}>
                      <Brain className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => changeTheme()}>
                      <Palette className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => openTipModal()}>
                      <DollarSign className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              {/* Messages Area */}
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {messages.length === 0 ? (
                    <div className="text-center text-sm text-muted-foreground">
                      No messages yet. Start the conversation! 🎤
                    </div>
                  ) : (
                    messages.map((message) => (
                      <div key={message.id} className="flex gap-3">
                        <Avatar className="h-8 w-8 flex-shrink-0">
                          <AvatarImage src={message.sender_profile?.avatar_url} />
                          <AvatarFallback>
                            {message.sender_profile?.display_name?.slice(0, 2).toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium">
                              {message.sender_profile?.display_name || 'Unknown User'}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              {new Date(message.created_at).toLocaleTimeString()}
                            </span>
                            {message.message_type === 'voice' && (
                              <Mic className="h-3 w-3 text-blue-500" />
                            )}
                            {message.message_type === 'emotion' && (
                              <Heart className="h-3 w-3 text-red-500" />
                            )}
                          </div>

                          {message.message_type === 'text' && (
                            <>
                              {message.content?.startsWith('📷 Image:') ? (
                                <div className="max-w-xs">
                                  <img
                                    src={message.content.replace('📷 Image: ', '')}
                                    alt="Shared image"
                                    className="rounded-lg max-w-full h-auto cursor-pointer"
                                    onClick={() => window.open(message.content?.replace('📷 Image: ', ''), '_blank')}
                                  />
                                </div>
                              ) : (
                                <p className="text-sm">{message.content}</p>
                              )}
                            </>
                          )}

                          {message.message_type === 'voice' && (
                            <div className="bg-muted p-3 rounded-lg max-w-xs">
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                >
                                  <Play className="h-4 w-4" />
                                </Button>
                                <div className="flex-1">
                                  <div className="h-2 bg-primary/20 rounded-full">
                                    <div className="h-2 bg-primary rounded-full w-0"></div>
                                  </div>
                                </div>
                                <span className="text-xs text-muted-foreground">
                                  {message.voice_duration ? `${Math.round(message.voice_duration)}s` : '0s'}
                                </span>
                              </div>
                              {message.voice_transcript && (
                                <p className="text-xs text-muted-foreground mt-2">
                                  "{message.voice_transcript}"
                                </p>
                              )}
                              <audio src={message.voice_url} preload="metadata" />
                            </div>
                          )}

                          {/* Message Reactions */}
                          <div className="mt-2">
                            <MessageReactions
                              messageId={message.id}
                              userId={user?.id || ''}
                            />
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Input Area */}
              <div className="border-t bg-card p-4">
                {/* Image Preview */}
                {imagePreview && (
                  <div className="mb-3 p-2 bg-muted rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Image Preview:</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setSelectedImage(null);
                          setImagePreview(null);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <img src={imagePreview} alt="Preview" className="max-w-32 h-auto rounded" />
                    <Button onClick={sendImageMessage} size="sm" className="mt-2 w-full">
                      Send Image 📷
                    </Button>
                  </div>
                )}

                <div className="flex items-center gap-2">
                  <Button
                    variant={isRecording ? "destructive" : "outline"}
                    size="sm"
                    onClick={startVoiceMessage}
                    disabled={isProcessingVoice}
                  >
                    {isProcessingVoice ? (
                      <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full" />
                    ) : isRecording ? (
                      <MicOff className="h-4 w-4" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>

                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type a message or speak your mind..."
                    className="flex-1"
                    disabled={isProcessingVoice}
                  />

                  <Button variant="ghost" size="sm">
                    <Smile className="h-4 w-4" />
                  </Button>

                  <label htmlFor="image-upload">
                    <Button variant="ghost" size="sm" asChild>
                      <span>
                        <Paperclip className="h-4 w-4" />
                      </span>
                    </Button>
                  </label>
                  <input
                    id="image-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />

                  <Button
                    size="sm"
                    onClick={sendTextMessage}
                    disabled={!newMessage.trim() || isProcessingVoice}
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>

                {/* Emotional Wave Buttons */}
                <div className="flex items-center gap-2 mt-2">
                  <span className="text-xs text-muted-foreground">Send wave:</span>
                  {['💙', '🔥', '⚡', '🌟', '🌊'].map((emoji, i) => (
                    <Button
                      key={i}
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0"
                      onClick={() => sendEmotionalWave(emoji)}
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Welcome to VoiceWave Chat</h3>
                <p className="text-muted-foreground mb-4">
                  Select a chat to start your revolutionary messaging experience
                </p>
                <div className="flex gap-2 justify-center">
                  <Button onClick={() => createNewChat('direct')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Start Direct Chat
                  </Button>
                  <Button variant="outline" onClick={() => createNewChat('group')}>
                    <Users className="h-4 w-4 mr-2" />
                    Create Group
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Chat Modal */}
      <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              Create {createChatType === 'direct' ? 'Direct Chat' :
                     createChatType === 'group' ? 'Group Chat' :
                     'Wave Chat'}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="chatName">Chat Name</Label>
              <Input
                id="chatName"
                value={chatName}
                onChange={(e) => setChatName(e.target.value)}
                placeholder={`Enter ${createChatType} chat name...`}
              />
            </div>

            {createChatType !== 'direct' && (
              <div>
                <Label htmlFor="chatDescription">Description (Optional)</Label>
                <Textarea
                  id="chatDescription"
                  value={chatDescription}
                  onChange={(e) => setChatDescription(e.target.value)}
                  placeholder="Describe your chat..."
                  rows={3}
                />
              </div>
            )}

            {createChatType === 'group' && (
              <>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPrivate"
                    checked={isPrivate}
                    onCheckedChange={setIsPrivate}
                  />
                  <Label htmlFor="isPrivate">Private Group</Label>
                </div>

                <div>
                  <Label htmlFor="participants">Invite Participants</Label>
                  <Textarea
                    id="participants"
                    value={participantEmails}
                    onChange={(e) => setParticipantEmails(e.target.value)}
                    placeholder="Enter email addresses or usernames, one per line..."
                    rows={4}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Add participants by email or username
                  </p>
                </div>
              </>
            )}

            {createChatType === 'wave' && (
              <div className="bg-muted p-4 rounded-lg">
                <h4 className="font-medium mb-2">🌊 Wave Chat Features:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Send emotional energy waves</li>
                  <li>• Visual mood representation</li>
                  <li>• Dynamic theme changes</li>
                  <li>• Collective emotional experience</li>
                </ul>
              </div>
            )}

            <div className="flex gap-2 pt-4">
              <Button
                onClick={handleCreateChat}
                className="flex-1"
                disabled={!chatName.trim()}
              >
                Create {createChatType === 'direct' ? 'Chat' :
                       createChatType === 'group' ? 'Group' :
                       'Wave'}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowCreateModal(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>VoiceWave Chat Settings</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Notification Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Sound notifications</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Desktop notifications</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Voice Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Auto-transcription</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Voice enhancement</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Privacy Settings</h4>
              <div className="flex items-center justify-between">
                <Label>Show online status</Label>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <Label>Allow emotional waves</Label>
                <Switch defaultChecked />
              </div>
            </div>

            <Button onClick={() => setShowSettingsModal(false)} className="w-full">
              Save Settings
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* AI Assistant Modal */}
      <Dialog open={showAIModal} onOpenChange={setShowAIModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>🧠 AI Assistant</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="bg-muted p-4 rounded-lg">
              <h4 className="font-medium mb-2">AI Features Available:</h4>
              <ul className="text-sm space-y-1">
                <li>• Smart message suggestions</li>
                <li>• Voice transcription & translation</li>
                <li>• Emotional tone analysis</li>
                <li>• Conversation summaries</li>
                <li>• User summoning predictions</li>
              </ul>
            </div>

            <div className="space-y-2">
              <Label>Ask AI Assistant</Label>
              <Input placeholder="How can I help you with your conversations?" />
            </div>

            <div className="flex gap-2">
              <Button className="flex-1">
                <Brain className="h-4 w-4 mr-2" />
                Ask AI
              </Button>
              <Button variant="outline" onClick={() => setShowAIModal(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Join Space Modal */}
      <Dialog open={showSpaceModal} onOpenChange={setShowSpaceModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Join Voice Space</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Space ID or Invite Link</Label>
              <Input placeholder="Enter space ID or paste invite link..." />
            </div>

            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <h4 className="font-medium mb-2">🌍 Spatial Audio Features:</h4>
              <ul className="text-sm space-y-1">
                <li>• 3D positioned voices</li>
                <li>• Immersive audio experience</li>
                <li>• Move around virtual space</li>
                <li>• Distance-based volume</li>
              </ul>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1">
                <Headphones className="h-4 w-4 mr-2" />
                Join Space
              </Button>
              <Button variant="outline" onClick={() => setShowSpaceModal(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Tip Modal */}
      {showTipModal && (
        <TipModalWithoutWallet
          isOpen={showTipModal}
          onClose={() => setShowTipModal(false)}
          recipientAddress={tipRecipientId}
        />
      )}
    </div>
  );
};

export default VoiceChatHub;
