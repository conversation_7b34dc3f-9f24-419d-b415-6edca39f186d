import React, { useState, useCallback, useEffect } from 'react';
import { Routes, Route } from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner, toast } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import IndexWithoutChannel from "@/pages/IndexWithoutChannel";
import NotFound from "@/pages/NotFound";
// Channels removed
import Notifications from "@/pages/Notifications";
import ProfileWithoutChannel from "@/pages/ProfileWithoutChannel";
import Settings from "@/pages/Settings";
import ChainVoiceMoments from "@/pages/ChainVoiceMoments";
import VoiceJournals from "@/pages/VoiceJournals";
import PostDetail from "@/pages/PostDetail";
import Analytics from "@/pages/Analytics";
import Wallet from "@/pages/Wallet";
import Login from "@/pages/Login";
import Signup from "@/pages/Signup";
import ForgotPassword from "@/pages/ForgotPassword";
import Welcome from "@/pages/Welcome";
import AdminVerification from "@/pages/AdminVerification";
import Layout from "@/components/Layout";
import ReplyModal from "@/components/ReplyModal";
import ProtectedRoute from "@/components/ProtectedRoute";
import { VoiceMessageProps } from "@/types/voice-message";
import { MediaFile } from "@/components/MediaUploader";
import { useNotifications } from '@/contexts/NotificationContext';
import { useAuth } from '@/contexts/AuthContext';
import { filterDeletedPosts } from '@/services/voiceMessageService.js';

interface AppContentProps {
  connectedAccount: string;
  setConnectedAccount: (address: string) => void;
}

const AppContent: React.FC<AppContentProps> = ({
  connectedAccount,
  setConnectedAccount
}) => {
  // Initialize messages state
  const [messages, setMessages] = useState<VoiceMessageProps[]>([]);

  // Load messages from Supabase only
  useEffect(() => {
    const loadMessages = async () => {
      try {
        console.log('Loading messages from Supabase...');

        // Import the optimized service
        const { getAllVoiceMessagesOptimized } = await import('@/services/optimizedVoiceMessageService');

        // Get messages from Supabase using optimized query
        const dbMessages = await getAllVoiceMessagesOptimized();
        console.log(`Loaded ${dbMessages.length} messages from Supabase`);

        // Sort messages by timestamp (newest first)
        const sortedMessages = [...dbMessages].sort((a, b) => {
          const aTime = typeof a.timestamp === 'number' ? a.timestamp : new Date(a.timestamp).getTime();
          const bTime = typeof b.timestamp === 'number' ? b.timestamp : new Date(b.timestamp).getTime();
          return bTime - aTime;
        });

        // Update state with messages from database
        setMessages(sortedMessages);
      } catch (error) {
        console.error('Error loading messages from Supabase:', error);
        // No fallback to localStorage - only use Supabase
        setMessages([]);
      }
    };

    loadMessages();
  }, []);

  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);
  const [replyingToId, setReplyingToId] = useState<string | null>(null);
  const { addNotification } = useNotifications();

  // Get authentication state
  const { user } = useAuth();

  // We no longer need to save messages to localStorage
  // All messages are saved to Supabase directly

  // Handle new voice recordings
  const handleRecordingComplete = async (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    // Get the user address from auth context if available
    const authUserAddress = user?.walletAddress;

    // Use the auth user address first, then the prop
    // This ensures consistency across the application
    const userAccount = authUserAddress || connectedAccount;

    if (!userAccount) {
      toast.error("No connected account found. Please log in again.");
      return;
    }

    console.log(`Creating voice message for account: ${userAccount}`);
    console.log(`Media received in handleRecordingComplete:`, media);
    console.log(`Number of media files:`, media?.length || 0);

    // Generate a unique ID for the message that will be consistent across devices
    // Use UUID v4 for better uniqueness
    const messageId = crypto.randomUUID();
    console.log(`Generated message ID: ${messageId}`);

    try {
      // Show a loading toast
      const loadingToast = toast.loading('Saving your voice message...');

      // Import the storage service factory
      const { getStorageService } = (await import('@/services/storageServiceFactory'));

      // Get the appropriate storage service based on user settings
      const storageService = await getStorageService();

      // Upload the audio blob to storage
      const audioUrl = await storageService.uploadAudio(audioBlob, userAccount);
      console.log('Audio uploaded successfully:', audioUrl);

      // Calculate or use provided duration
      let actualDuration = duration;

      if (!actualDuration || !isFinite(actualDuration)) {
        console.log('Duration not provided, calculating from audio element');

        // Get accurate audio duration
        const audio = new Audio(audioUrl);

        // Create a promise to get the duration
        const getDuration = () => {
          return new Promise<number>((resolve) => {
            audio.addEventListener('loadedmetadata', () => {
              if (audio.duration !== Infinity) {
                resolve(audio.duration);
              } else {
                // Fallback if duration is Infinity
                audio.currentTime = 24 * 60 * 60; // Seek to 24 hours
                audio.addEventListener('timeupdate', function getDurationFromTimeUpdate() {
                  if (audio.currentTime > 0) {
                    resolve(audio.duration);
                    audio.removeEventListener('timeupdate', getDurationFromTimeUpdate);
                  }
                });
              }
            });

            // Handle errors
            audio.addEventListener('error', () => {
              console.error('Error loading audio for duration calculation');
              resolve(30); // Default duration as fallback
            });

            // Load the audio
            audio.load();
          });
        };

        // Get the duration
        actualDuration = await getDuration();
      }

      console.log(`Using duration: ${actualDuration}s`);
      console.log(`Media files attached: ${media?.length || 0}`);

      // Create a new message object
      const newMessage: VoiceMessageProps = {
        id: messageId,
        audioUrl,
        transcript,
        userAddress: userAccount,
        timestamp: new Date(),
        duration: actualDuration,
        replies: [],
        media: media || [] // Ensure media is always an array
      };

      // Save to database first
      try {
        // Upload media files to permanent storage first
        let uploadedMedia: any[] = [];
        if (media && media.length > 0) {
          console.log(`🔥 MEDIA UPLOAD START: Found ${media.length} media files to upload`);
          console.log('Media details:', media.map(m => ({ id: m.id, type: m.type, hasFile: !!m.file, url: m.url })));

          try {
            const { uploadFile } = await import('@/services/supabaseStorageService');

            uploadedMedia = await Promise.all(
              media.map(async (mediaItem) => {
                try {
                  // Convert blob URL back to file if needed
                  if (mediaItem.file) {
                    // Generate a unique file path
                    const fileExtension = mediaItem.file.name.split('.').pop() || 'jpg';
                    const fileName = `${userAccount}/${messageId}_${mediaItem.id}.${fileExtension}`;

                    // Upload to Supabase storage
                    const uploadedUrl = await uploadFile(mediaItem.file, 'media', fileName);

                    if (uploadedUrl) {
                      console.log(`Successfully uploaded media file: ${uploadedUrl}`);
                      return {
                        id: mediaItem.id,
                        url: uploadedUrl,
                        type: mediaItem.type
                      };
                    } else {
                      console.warn(`Failed to upload media file: ${mediaItem.file.name}`);
                      return null;
                    }
                  }
                  return null;
                } catch (uploadError) {
                  console.error('Error uploading media file:', uploadError);
                  return null;
                }
              })
            );

            // Filter out failed uploads
            uploadedMedia = uploadedMedia.filter(item => item !== null);
            console.log(`Successfully uploaded ${uploadedMedia.length} media files`);
          } catch (mediaUploadError) {
            console.error('Error uploading media files:', mediaUploadError);
            // Continue without media if upload fails
            uploadedMedia = [];
          }
        }

        // Import the module
        const voiceMessageModule = await import('@/services/voiceMessageService.js');

        // Use type assertion to tell TypeScript that the function accepts 6 parameters
        type SaveVoiceMessageFn = (
          audioUrl: string,
          transcript: string,
          userAddress: string,
          duration: number,
          media?: any[],
          customId?: string | null
        ) => Promise<VoiceMessageProps>;

        // Cast the function to the correct type
        const saveVoiceMessageFn = voiceMessageModule.saveVoiceMessage as SaveVoiceMessageFn;

        // Now call the function with all parameters, using uploaded media
        const savedMessage = await saveVoiceMessageFn(
          audioUrl,
          transcript,
          userAccount,
          actualDuration,
          uploadedMedia, // Use uploaded media instead of blob URLs
          messageId // Pass the generated ID to ensure consistency
        );

        console.log('Message saved to database successfully:', savedMessage);

        // Use the returned message from the database to ensure consistency
        if (savedMessage && savedMessage.id) {
          // Add to messages state, checking for duplicates
          setMessages(prev => {
            // Check if this message already exists in the state
            const exists = prev.some(msg => msg.id === savedMessage.id);
            if (exists) {
              console.log(`Message ${savedMessage.id} already exists in state, not adding duplicate`);
              return prev;
            }
            // Add the new message at the beginning of the array
            return [savedMessage, ...prev];
          });
        } else {
          // If the database save didn't return a valid message, use our local one
          setMessages(prev => [newMessage, ...prev]);
        }

        // Dismiss the loading toast and show success
        toast.dismiss(loadingToast);
        toast.success('Voice message saved successfully!');
      } catch (dbError) {
        console.error('Error saving message to database:', dbError);

        // If database save fails, still update local state
        setMessages(prev => {
          // Check if this message already exists in the state
          const exists = prev.some(msg => msg.id === messageId);
          if (exists) {
            return prev;
          }
          return [newMessage, ...prev];
        });

        // Dismiss the loading toast and show warning
        toast.dismiss(loadingToast);
        toast.warning('Voice message saved locally only. It may not appear on other devices.');
      }
    } catch (error) {
      console.error('Error processing audio:', error);

      // Fall back to blob URL if upload fails
      const tempAudioUrl = URL.createObjectURL(audioBlob);

      // Create a new message with the blob URL
      const newMessage: VoiceMessageProps = {
        id: messageId,
        audioUrl: tempAudioUrl,
        transcript,
        userAddress: userAccount,
        timestamp: new Date(),
        duration: duration || 30, // Default duration
        replies: [],
        media: media || [] // Ensure media is always an array
      };

      // Add to messages
      setMessages(prev => {
        // Check if this message already exists in the state
        const exists = prev.some(msg => msg.id === messageId);
        if (exists) {
          return prev;
        }
        return [newMessage, ...prev];
      });

      toast.error('Failed to save voice message to cloud storage. It will be available only on this device.');
    }
  };

  // Handle reply button click
  const handleReply = useCallback((parentId: string) => {
    setReplyingToId(parentId);
    setIsReplyModalOpen(true);
  }, []);

  // Find the parent message by ID
  const findMessageById = useCallback((id: string): VoiceMessageProps | null => {
    for (const message of messages) {
      if (message.id === id) {
        return message;
      }
    }
    return null;
  }, [messages]);

  // Handle reply recording complete
  const handleReplyComplete = useCallback(async (parentId: string, audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    // Get the user address from auth context if available
    const authUserAddress = user?.walletAddress;

    // Use the auth user address first, then the prop
    // This ensures consistency across the application
    const userAccount = authUserAddress || connectedAccount;

    if (!userAccount || !parentId) {
      toast.error("No connected account found. Please log in again.");
      return;
    }

    try {
      // Import the storage service factory
      const { getStorageService } = (await import('@/services/storageServiceFactory'));

      // Get the appropriate storage service based on user settings
      const storageService = await getStorageService();

      // Upload the audio blob to storage
      const audioUrl = await storageService.uploadAudio(audioBlob, userAccount);

      console.log('Reply audio uploaded successfully:', audioUrl);

      // If duration is provided directly, use it
      if (duration && isFinite(duration)) {
        console.log(`Using provided duration for reply: ${duration}s`);
        console.log(`Media files attached to reply: ${media?.length || 0}`);

        const replyId = Date.now().toString();

        // Create a new reply message with the provided duration and media
        const newReply: VoiceMessageProps = {
          id: replyId,
          audioUrl,
          transcript,
          userAddress: userAccount,
          timestamp: new Date(),
          duration: duration,
          parentId,
          isReply: true,
          media: media || [], // Ensure media is always an array
          replies: [] // Initialize with empty replies array
        };

        // Find the parent message to get the owner's address
        const parentMessage = findMessageById(parentId);

        if (parentMessage && parentMessage.userAddress !== userAccount) {
          // Create a notification for the parent message owner
          addNotification(
            'reply',
            userAccount,
            parentMessage.userAddress,
            parentId,
            {
              replyId,
              text: transcript.substring(0, 50) + (transcript.length > 50 ? '...' : '')
            }
          );
        }

        // Add the reply to the parent message
        setMessages(prev => {
          return prev.map(message => {
            if (message.id === parentId) {
              return {
                ...message,
                replies: [...(message.replies || []), newReply]
              };
            }
            return message;
          });
        });

        return;
      }

      // If no duration provided, calculate it
      console.log('Duration not provided for reply, calculating from audio element');

      // Get accurate audio duration
      const audio = new Audio(audioUrl);

      // Create a promise to get the duration
      const getDuration = () => {
        return new Promise<number>((resolve) => {
          audio.addEventListener('loadedmetadata', () => {
            if (audio.duration !== Infinity) {
              resolve(audio.duration);
            } else {
              // Fallback if duration is Infinity
              audio.currentTime = 24 * 60 * 60; // Seek to 24 hours
              audio.addEventListener('timeupdate', function getDurationFromTimeUpdate() {
                if (audio.currentTime > 0) {
                  resolve(audio.duration);
                  audio.removeEventListener('timeupdate', getDurationFromTimeUpdate);
                }
              });
            }
          });
        });
      };

      // Get the duration and then save the reply to database
      getDuration().then(async (actualDuration) => {
        try {
          console.log(`Calculated duration for reply: ${actualDuration}s`);
          console.log(`Media files attached to reply: ${media?.length || 0}`);

          // Upload media files to permanent storage first
          let uploadedMedia: any[] = [];
          if (media && media.length > 0) {
            console.log(`Uploading ${media.length} reply media files to storage...`);

            try {
              const { uploadFile } = await import('@/services/supabaseStorageService');

              uploadedMedia = await Promise.all(
                media.map(async (mediaItem) => {
                  try {
                    // Convert blob URL back to file if needed
                    if (mediaItem.file) {
                      // Generate a unique file path
                      const fileExtension = mediaItem.file.name.split('.').pop() || 'jpg';
                      const fileName = `${userAccount}/reply_${Date.now()}_${mediaItem.id}.${fileExtension}`;

                      // Upload to Supabase storage
                      const uploadedUrl = await uploadFile(mediaItem.file, 'media', fileName);

                      if (uploadedUrl) {
                        console.log(`Successfully uploaded reply media file: ${uploadedUrl}`);
                        return {
                          id: mediaItem.id,
                          url: uploadedUrl,
                          type: mediaItem.type
                        };
                      } else {
                        console.warn(`Failed to upload reply media file: ${mediaItem.file.name}`);
                        return null;
                      }
                    }
                    return null;
                  } catch (uploadError) {
                    console.error('Error uploading reply media file:', uploadError);
                    return null;
                  }
                })
              );

              // Filter out failed uploads
              uploadedMedia = uploadedMedia.filter(item => item !== null);
              console.log(`Successfully uploaded ${uploadedMedia.length} reply media files`);
            } catch (mediaUploadError) {
              console.error('Error uploading reply media files:', mediaUploadError);
              // Continue without media if upload fails
              uploadedMedia = [];
            }
          }

          // Save reply to database
          console.log('=== SAVING REPLY TO DATABASE ===');
          console.log('Parent ID:', parentId);
          console.log('User Account:', userAccount);
          console.log('Transcript:', transcript);
          console.log('Duration:', actualDuration);
          console.log('Media count:', uploadedMedia.length);

          const voiceMessageModule = await import('@/services/voiceMessageService.js');
          const savedReply = await voiceMessageModule.saveReply(
            audioUrl,
            transcript,
            userAccount,
            actualDuration,
            parentId,
            uploadedMedia
          );

          console.log('✅ Reply saved to database successfully:', savedReply.id);

          // Find the parent message to get the owner's address
          const parentMessage = findMessageById(parentId);

          if (parentMessage && parentMessage.userAddress !== userAccount) {
            // Create a notification for the parent message owner
            addNotification(
              'reply',
              userAccount,
              parentMessage.userAddress,
              parentId,
              {
                replyId: savedReply.id,
                text: transcript.substring(0, 50) + (transcript.length > 50 ? '...' : '')
              }
            );
          }

          // Add the reply to the parent message in local state
          setMessages(prev => {
            return prev.map(message => {
              if (message.id === parentId) {
                return {
                  ...message,
                  replies: [...(message.replies || []), savedReply]
                };
              }
              return message;
            });
          });

          toast.success('Reply saved successfully!');
        } catch (error) {
          console.error('Error saving reply:', error);
          console.error('Error details:', error.message);

          // Show more specific error message
          if (error.message.includes('Profile not found')) {
            toast.error('Profile not found. Please make sure you have a valid profile.');
          } else if (error.message.includes('Failed to save reply')) {
            toast.error('Failed to save reply to database. Please try again.');
          } else {
            toast.error('Failed to save reply. Please try again.');
          }
        }
      });
    } catch (error) {
      console.error('Error processing reply audio:', error);
      // Fall back to blob URL if upload fails
      const tempAudioUrl = URL.createObjectURL(audioBlob);

      const replyId = Date.now().toString();

      // Create a new reply with the blob URL
      const newReply: VoiceMessageProps = {
        id: replyId,
        audioUrl: tempAudioUrl,
        transcript,
        userAddress: userAccount,
        timestamp: new Date(),
        duration: duration || 30, // Default duration
        parentId,
        isReply: true,
        media: media || [], // Ensure media is always an array
        replies: [] // Initialize with empty replies array
      };

      // Add the reply to the parent message
      setMessages(prev => {
        return prev.map(message => {
          if (message.id === parentId) {
            return {
              ...message,
              replies: [...(message.replies || []), newReply]
            };
          }
          return message;
        });
      });
    }
  }, [connectedAccount, findMessageById, addNotification, user]);

  // Use user ID from auth context if available, fallback to connectedAccount
  const userAddress = user?.id || connectedAccount;

  // Update connected account when user logs in
  useEffect(() => {
    if (user?.id) {
      // Use the user's UUID, not their wallet address
      // This ensures consistency with Supabase auth
      setConnectedAccount(user.id);
    }
  }, [user, setConnectedAccount]);

  return (
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <Routes>
        {/* Public Routes */}
        <Route path="/welcome" element={<Welcome />} />
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<Signup />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />

        {/* Protected Routes */}
        <Route path="/" element={
          <ProtectedRoute>
            <Layout
              connectedAccount={userAddress}
              onWalletConnect={setConnectedAccount}
              onRecordingComplete={handleRecordingComplete}
            >
              <IndexWithoutChannel
                connectedAccount={userAddress}
                messages={filterDeletedPosts(messages)}
                onReply={handleReply}
              />
            </Layout>
          </ProtectedRoute>
        } />
        {/* CHANNELS ROUTE REMOVED - FUNCTIONALITY WAS BROKEN */}
        <Route path="/notifications" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <Notifications />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <ProfileWithoutChannel userAddress={userAddress} />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile/:address" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <ProfileWithoutChannel />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/settings" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <Settings userAddress={userAddress} />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/chain-voice" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <ChainVoiceMoments />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/chain-voice/:filter/:value" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <ChainVoiceMoments />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/journals" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <VoiceJournals userAddress={userAddress} />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/post/:id" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <PostDetail
                messages={filterDeletedPosts(messages)}
                onReply={handleReply}
                connectedAccount={userAddress}
              />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/analytics" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <Analytics
                messages={messages}
                userAddress={userAddress}
              />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/wallet" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <Wallet />
            </Layout>
          </ProtectedRoute>
        } />

        {/* Admin Routes */}
        <Route path="/admin/verification" element={
          <ProtectedRoute>
            <Layout connectedAccount={userAddress} onWalletConnect={setConnectedAccount}>
              <AdminVerification />
            </Layout>
          </ProtectedRoute>
        } />

        {/* Catch-all Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>

      {/* Reply Modal */}
      {replyingToId && (
        <ReplyModal
          isOpen={isReplyModalOpen}
          onClose={() => setIsReplyModalOpen(false)}
          onReplyComplete={handleReplyComplete}
          parentId={replyingToId}
        />
      )}
    </TooltipProvider>
  );
};

export default AppContent;
