import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Mic, 
  MicOff, 
  Hand, 
  MessageSquare, 
  Users, 
  Settings, 
  PhoneOff,
  Send,
  Crown,
  Volume2,
  VolumeX
} from 'lucide-react';
import { spaceService, type LiveStream, type StreamParticipant, type StreamChatMessage } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';

interface SpaceViewerProps {
  spaceId: string;
  isOpen: boolean;
  onClose: () => void;
  currentUserId: string;
}

export const SpaceViewer: React.FC<SpaceViewerProps> = ({
  spaceId,
  isOpen,
  onClose,
  currentUserId,
}) => {
  const [space, setSpace] = useState<LiveStream | null>(null);
  const [participants, setParticipants] = useState<StreamParticipant[]>([]);
  const [chatMessages, setChatMessages] = useState<StreamChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [hasMicAccess, setHasMicAccess] = useState(false);
  const [micRequested, setMicRequested] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [isHost, setIsHost] = useState(false);
  
  const { toast } = useToast();
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null);

  useEffect(() => {
    if (isOpen && spaceId) {
      loadSpaceData();
      joinSpace();
      
      // Subscribe to real-time updates
      subscriptionRef.current = spaceService.subscribeToStream(spaceId, {
        onParticipantJoined: (participant) => {
          setParticipants(prev => [...prev, participant]);
        },
        onParticipantLeft: (participant) => {
          setParticipants(prev => prev.filter(p => p.id !== participant.id));
        },
        onChatMessage: (message) => {
          setChatMessages(prev => [...prev, message]);
          scrollToBottom();
        },
        onStreamUpdate: (updatedStream) => {
          setSpace(updatedStream);
        },
      });
    }

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [isOpen, spaceId]);

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  const loadSpaceData = async () => {
    try {
      // Get space details
      const spaces = await spaceService.getLiveStreams();
      const currentSpace = spaces.find(s => s.id === spaceId);
      if (currentSpace) {
        setSpace(currentSpace);
        setIsHost(currentSpace.host_profile_id === currentUserId);
      }

      // Get participants
      const spaceParticipants = await spaceService.getStreamParticipants(spaceId);
      setParticipants(spaceParticipants);

      // Check if current user is already joined
      const currentUserParticipant = spaceParticipants.find(p => p.profile_id === currentUserId);
      if (currentUserParticipant) {
        setIsJoined(true);
        setHasMicAccess(['host', 'co_host', 'speaker'].includes(currentUserParticipant.role));
      }

      // Get chat messages
      const messages = await spaceService.getChatMessages(spaceId);
      setChatMessages(messages);
    } catch (error) {
      console.error('Error loading space data:', error);
    }
  };

  const joinSpace = async () => {
    if (!isJoined) {
      const success = await spaceService.joinStream(spaceId, currentUserId);
      if (success) {
        setIsJoined(true);
        toast({
          title: "Joined space",
          description: "You're now listening to this space.",
        });
      }
    }
  };

  const leaveSpace = async () => {
    const success = await spaceService.leaveStream(spaceId, currentUserId);
    if (success) {
      setIsJoined(false);
      onClose();
    }
  };

  const requestMic = async () => {
    const success = await spaceService.requestMicAccess(spaceId, currentUserId);
    if (success) {
      setMicRequested(true);
      toast({
        title: "Mic request sent",
        description: "The host will review your request.",
      });
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    const success = await spaceService.sendChatMessage(spaceId, currentUserId, newMessage.trim());
    if (success) {
      setNewMessage('');
    } else {
      toast({
        title: "Failed to send message",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const scrollToBottom = () => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'host':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'co_host':
        return <Crown className="h-3 w-3 text-blue-500" />;
      case 'speaker':
        return <Mic className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const speakers = participants.filter(p => ['host', 'co_host', 'speaker'].includes(p.role));
  const listeners = participants.filter(p => p.role === 'listener');

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] h-[600px] p-0">
        <div className="flex h-full">
          {/* Main Space Area */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-2">
                <h1 className="text-lg font-semibold">{space?.title}</h1>
                <Badge variant="destructive" className="animate-pulse">
                  🔴 LIVE
                </Badge>
              </div>
              {space?.description && (
                <p className="text-sm text-muted-foreground">{space.description}</p>
              )}
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {participants.length} listening
                </span>
                <span className="flex items-center gap-1">
                  <Mic className="h-4 w-4" />
                  {speakers.length} speaking
                </span>
              </div>
            </div>

            {/* Speakers Section */}
            <div className="flex-1 p-4">
              <h3 className="text-sm font-medium mb-3">On Stage</h3>
              <div className="grid grid-cols-3 gap-4 mb-6">
                {speakers.map((participant) => (
                  <div key={participant.id} className="text-center">
                    <div className="relative mb-2">
                      <Avatar className="w-16 h-16 mx-auto">
                        <AvatarImage src="" />
                        <AvatarFallback>
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -top-1 -right-1">
                        {getRoleIcon(participant.role)}
                      </div>
                      {participant.is_muted ? (
                        <MicOff className="absolute -bottom-1 right-0 h-4 w-4 bg-red-500 text-white rounded-full p-1" />
                      ) : (
                        <Mic className="absolute -bottom-1 right-0 h-4 w-4 bg-green-500 text-white rounded-full p-1" />
                      )}
                    </div>
                    <p className="text-xs truncate">
                      {participant.profile_id.slice(0, 8)}...
                    </p>
                  </div>
                ))}
              </div>

              {/* Listeners */}
              {listeners.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Listeners ({listeners.length})</h3>
                  <div className="flex flex-wrap gap-2">
                    {listeners.slice(0, 20).map((participant) => (
                      <Avatar key={participant.id} className="w-8 h-8">
                        <AvatarImage src="" />
                        <AvatarFallback className="text-xs">
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    {listeners.length > 20 && (
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs">
                        +{listeners.length - 20}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="p-4 border-t">
              <div className="flex items-center justify-center gap-3">
                <Button
                  variant={isMuted ? "secondary" : "destructive"}
                  size="sm"
                  onClick={() => setIsMuted(!isMuted)}
                  disabled={!hasMicAccess}
                >
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>

                {!hasMicAccess && !micRequested && (
                  <Button variant="outline" size="sm" onClick={requestMic}>
                    <Hand className="h-4 w-4 mr-2" />
                    Request to Speak
                  </Button>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowChat(!showChat)}
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>

                <Button variant="destructive" size="sm" onClick={leaveSpace}>
                  <PhoneOff className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Chat Sidebar */}
          {showChat && (
            <div className="w-80 border-l flex flex-col">
              <div className="p-3 border-b">
                <h3 className="font-medium">Chat</h3>
              </div>
              
              <ScrollArea className="flex-1 p-3" ref={chatScrollRef}>
                <div className="space-y-3">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="text-sm">
                      <div className="flex items-start gap-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">
                            {message.profile_id.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="text-xs text-muted-foreground">
                            {message.profile_id.slice(0, 8)}...
                          </p>
                          <p>{message.message}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <form onSubmit={sendMessage} className="p-3 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    className="flex-1"
                  />
                  <Button type="submit" size="sm" disabled={!newMessage.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};