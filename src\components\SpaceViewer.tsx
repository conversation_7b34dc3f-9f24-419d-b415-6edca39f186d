import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Mi<PERSON>, 
  MicOff, 
  Hand, 
  MessageSquare, 
  Users, 
  Settings, 
  PhoneOff,
  Send,
  Crown,
  Volume2,
  VolumeX,
  DollarSign,
  X,
  Minimize2
} from 'lucide-react';
import { spaceService, type LiveStream, type StreamParticipant, type StreamChatMessage } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface SpaceViewerProps {
  spaceId: string;
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  currentUserId: string;
}

export const SpaceViewer: React.FC<SpaceViewerProps> = ({
  spaceId,
  isOpen,
  onClose,
  onMinimize,
  currentUserId,
}) => {
  const [space, setSpace] = useState<LiveStream | null>(null);
  const [participants, setParticipants] = useState<StreamParticipant[]>([]);
  const [chatMessages, setChatMessages] = useState<StreamChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isJoined, setIsJoined] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [micRequests, setMicRequests] = useState<StreamParticipant[]>([]);
  const [reactions, setReactions] = useState<{emoji: string, id: string, timestamp: number}[]>([]);
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipTarget, setTipTarget] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState(false);
  const chatScrollRef = useRef<HTMLDivElement>(null);

  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && spaceId) {
      loadSpaceData();
    }
  }, [isOpen, spaceId]);

  const loadSpaceData = async () => {
    try {
      setIsLoading(true);
      // Get all live streams and find the one we need
      const allStreams = await spaceService.getLiveStreams();
      const spaceData = allStreams.find(stream => stream.id === spaceId);

      if (spaceData) {
        setSpace(spaceData);
        setIsHost(spaceData.host_profile_id === currentUserId);

        // Auto-join the space
        const joinSuccess = await spaceService.joinStream(spaceId, currentUserId);
        setIsJoined(joinSuccess);

        // Load participants and messages
        await loadParticipants();
        await loadChatMessages();
      }
    } catch (error) {
      console.error('Error loading space data:', error);
      toast({ title: "Failed to load space", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const loadParticipants = async () => {
    try {
      const participantsList = await spaceService.getStreamParticipants(spaceId);
      setParticipants(participantsList);
    } catch (error) {
      console.error('Error loading participants:', error);
    }
  };

  const loadChatMessages = async () => {
    try {
      const messages = await spaceService.getChatMessages(spaceId);
      setChatMessages(messages);
    } catch (error) {
      console.error('Error loading chat messages:', error);
    }
  };

  const leaveSpace = async () => {
    const success = await spaceService.leaveStream(spaceId, currentUserId);
    if (success) {
      setIsJoined(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] h-[600px] p-0">
        <DialogTitle className="sr-only">
          {space?.title || 'Space Viewer'}
        </DialogTitle>
        <div className="flex h-full">
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h1 className="text-lg font-semibold">{space?.title || 'Loading...'}</h1>
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="animate-pulse">
                    🔴 LIVE
                  </Badge>
                  {onMinimize && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMinimize}
                      className="hover:bg-gray-100"
                    >
                      <Minimize2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
            
            {/* Main Content */}
            <div className="flex-1 p-4">
              <div className="text-center">
                <h3 className="text-lg font-medium mb-2">Space Viewer</h3>
                <p className="text-gray-600 mb-4">
                  {space ? `Connected to: ${space.title}` : 'Loading space...'}
                </p>
                <p className="text-sm text-gray-500">
                  Participants: {participants.length}
                </p>
                {isHost && (
                  <Badge variant="secondary" className="mt-2">
                    <Crown className="h-3 w-3 mr-1" />
                    Host
                  </Badge>
                )}
              </div>
            </div>
            
            {/* Controls */}
            <div className="p-4 border-t">
              <div className="flex items-center justify-center gap-3">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowChat(!showChat)}
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>
                
                {isHost ? (
                  <Button variant="destructive" size="sm" onClick={leaveSpace}>
                    <PhoneOff className="h-4 w-4 mr-2" />
                    End Space
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" onClick={leaveSpace}>
                    <PhoneOff className="h-4 w-4" />
                    Leave
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          {/* Chat Sidebar */}
          {showChat && (
            <div className="w-80 border-l flex flex-col">
              <div className="p-3 border-b">
                <h3 className="font-medium">Chat</h3>
              </div>
              
              <ScrollArea className="flex-1 p-3" ref={chatScrollRef}>
                <div className="space-y-3">
                  {chatMessages.length === 0 ? (
                    <p className="text-center text-gray-500 py-4">No messages yet</p>
                  ) : (
                    chatMessages.map((message) => (
                      <div key={message.id} className="text-sm">
                        <div className="flex items-start gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs">
                              {message.profile_id.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-xs text-muted-foreground">
                              {message.profile_id.slice(0, 8)}...
                            </p>
                            <p>{message.message}</p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>

              <div className="p-3 border-t">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type a message..."
                    className="flex-1"
                  />
                  <Button size="sm">
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
