import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Mi<PERSON>, 
  MicOff, 
  Hand, 
  MessageSquare, 
  Users, 
  Settings, 
  PhoneOff,
  Send,
  Crown,
  Volume2,
  VolumeX,
  DollarSign,
  X,
  Minimize2
} from 'lucide-react';
import { spaceService, type LiveStream, type StreamParticipant, type StreamChatMessage } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface SpaceViewerProps {
  spaceId: string;
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  currentUserId: string;
}

export const SpaceViewer: React.FC<SpaceViewerProps> = ({
  spaceId,
  isOpen,
  onClose,
  onMinimize,
  currentUserId,
}) => {
  const [profileId, setProfileId] = useState<string | null>(null);
  const [space, setSpace] = useState<LiveStream | null>(null);
  const [participants, setParticipants] = useState<StreamParticipant[]>([]);
  const [chatMessages, setChatMessages] = useState<StreamChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [hasMicAccess, setHasMicAccess] = useState(false);
  const [micRequested, setMicRequested] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [micRequests, setMicRequests] = useState<StreamParticipant[]>([]);
  const [reactions, setReactions] = useState<{emoji: string, id: string, timestamp: number}[]>([]);
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipTarget, setTipTarget] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState(false);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && spaceId && currentUserId) {
      getProfileId();
    }
  }, [isOpen, spaceId, currentUserId]);

  useEffect(() => {
    if (profileId && spaceId) {
      loadSpaceData();
    }
  }, [profileId, spaceId]);

  const getProfileId = async () => {
    try {
      // If currentUserId is already a UUID (not a wallet address), use it directly
      if (currentUserId.length === 36 && currentUserId.includes('-')) {
        setProfileId(currentUserId);
        return;
      }

      // Otherwise, get profile ID from wallet address
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('id')
        .eq('wallet_address', currentUserId.toLowerCase())
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        toast({ title: "Failed to load profile", variant: "destructive" });
        return;
      }

      if (profileData) {
        setProfileId(profileData.id);
      } else {
        toast({ title: "Profile not found", variant: "destructive" });
      }
    } catch (error) {
      console.error('Error getting profile ID:', error);
      toast({ title: "Failed to load profile", variant: "destructive" });
    }
  };

  const loadSpaceData = async () => {
    try {
      // Get all live streams and find the one we need
      const allStreams = await spaceService.getLiveStreams();
      const spaceData = allStreams.find(stream => stream.id === spaceId);

      if (spaceData && profileId) {
        setSpace(spaceData);
        setIsHost(spaceData.host_profile_id === profileId);

        // Load participants and messages first
        await loadParticipants();
        await loadChatMessages();

        // Check if user is already joined
        const currentUserParticipant = participants.find(p => p.profile_id === profileId);
        if (currentUserParticipant) {
          setIsJoined(true);
          setHasMicAccess(['host', 'co_host', 'speaker'].includes(currentUserParticipant.role));
        } else {
          // Auto-join the space if not already joined
          const joinSuccess = await spaceService.joinStream(spaceId, profileId);
          setIsJoined(joinSuccess);
        }
      }
    } catch (error) {
      console.error('Error loading space data:', error);
      toast({ title: "Failed to load space", variant: "destructive" });
    }
  };

  const loadParticipants = async () => {
    try {
      const participantsList = await spaceService.getStreamParticipants(spaceId);
      setParticipants(participantsList);
    } catch (error) {
      console.error('Error loading participants:', error);
    }
  };

  const loadChatMessages = async () => {
    try {
      const messages = await spaceService.getChatMessages(spaceId);
      setChatMessages(messages);
    } catch (error) {
      console.error('Error loading chat messages:', error);
    }
  };

  const leaveSpace = async () => {
    if (!profileId) return;
    const success = await spaceService.leaveStream(spaceId, profileId);
    if (success) {
      setIsJoined(false);
      onClose();
    }
  };

  const endSpace = async () => {
    if (!isHost) return;

    try {
      await spaceService.endStream(spaceId);
      toast({ title: "Space ended successfully" });
      onClose();
    } catch (error) {
      console.error('Error ending space:', error);
      toast({ title: "Failed to end space", variant: "destructive" });
    }
  };

  const requestMic = async () => {
    if (!profileId) return;
    const success = await spaceService.requestMicAccess(spaceId, profileId);
    if (success) {
      setMicRequested(true);
      toast({
        title: "Mic request sent",
        description: "The host will review your request.",
      });
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !profileId) return;

    const success = await spaceService.sendChatMessage(spaceId, profileId, newMessage.trim());
    if (success) {
      setNewMessage('');
    } else {
      toast({
        title: "Failed to send message",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const sendReaction = (emoji: string) => {
    const reactionId = Math.random().toString(36).substring(7);
    const newReaction = {
      emoji,
      id: reactionId,
      timestamp: Date.now()
    };

    setReactions(prev => [...prev, newReaction]);

    // Remove reaction after 3 seconds
    setTimeout(() => {
      setReactions(prev => prev.filter(r => r.id !== reactionId));
    }, 3000);
  };

  const openTipModal = (targetUserId: string) => {
    setTipTarget(targetUserId);
    setShowTipModal(true);
  };

  const sendTip = async (amount: number) => {
    if (!tipTarget) return;

    try {
      // TODO: Implement actual tip functionality
      toast({ title: `Sent $${amount} tip!` });
      setShowTipModal(false);
      setTipTarget(null);
    } catch (error) {
      console.error('Error sending tip:', error);
      toast({ title: "Failed to send tip", variant: "destructive" });
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'host':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'co_host':
        return <Crown className="h-3 w-3 text-blue-500" />;
      case 'speaker':
        return <Mic className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const speakers = participants.filter(p => ['host', 'co_host', 'speaker'].includes(p.role));
  const listeners = participants.filter(p => p.role === 'listener');

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] h-[600px] p-0">
        <DialogTitle className="sr-only">
          {space?.title || 'Space Viewer'}
        </DialogTitle>
        <div className="flex h-full">
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between">
                <h1 className="text-lg font-semibold">{space?.title || 'Loading...'}</h1>
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="animate-pulse">
                    🔴 LIVE
                  </Badge>
                  {onMinimize && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMinimize}
                      className="hover:bg-gray-100"
                    >
                      <Minimize2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
            
            {/* Space Description */}
            {space?.description && (
              <div className="px-4 pb-2">
                <p className="text-sm text-muted-foreground">{space.description}</p>
              </div>
            )}

            {/* Stats */}
            <div className="px-4 pb-4">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {participants.length} listening
                </span>
                <span className="flex items-center gap-1">
                  <Mic className="h-4 w-4" />
                  {speakers.length} speaking
                </span>
              </div>
            </div>

            {/* Floating Reactions */}
            <div className="absolute top-20 right-4 z-10 pointer-events-none">
              {reactions.map((reaction) => (
                <div
                  key={reaction.id}
                  className="text-2xl animate-bounce mb-2"
                  style={{
                    animation: `float-up 3s ease-out forwards`,
                  }}
                >
                  {reaction.emoji}
                </div>
              ))}
            </div>

            {/* Speakers Section */}
            <div className="flex-1 p-4">
              <h3 className="text-sm font-medium mb-3">On Stage</h3>
              <div className="grid grid-cols-3 gap-4 mb-6">
                {speakers.map((participant) => (
                  <div key={participant.id} className="text-center">
                    <div className="relative mb-2">
                      <Avatar className="w-16 h-16 mx-auto">
                        <AvatarFallback>
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -top-1 -right-1">
                        {getRoleIcon(participant.role)}
                      </div>
                      {participant.is_muted ? (
                        <MicOff className="absolute -bottom-1 right-0 h-4 w-4 bg-red-500 text-white rounded-full p-1" />
                      ) : (
                        <Mic className="absolute -bottom-1 right-0 h-4 w-4 bg-green-500 text-white rounded-full p-1" />
                      )}
                    </div>
                    <p className="text-xs truncate mb-1">
                      {participant.profile_id.slice(0, 8)}...
                    </p>
                    {participant.profile_id !== profileId && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openTipModal(participant.profile_id)}
                        className="text-xs h-6 px-2"
                      >
                        <DollarSign className="h-3 w-3 mr-1" />
                        Tip
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              {/* Listeners */}
              {listeners.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Listeners ({listeners.length})</h3>
                  <div className="flex flex-wrap gap-2">
                    {listeners.slice(0, 20).map((participant) => (
                      <Avatar key={participant.id} className="w-8 h-8">
                        <AvatarFallback className="text-xs">
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    {listeners.length > 20 && (
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs">
                        +{listeners.length - 20}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
            
            {/* Controls */}
            <div className="p-4 border-t">
              <div className="flex items-center justify-center gap-3">
                {/* Mic Control - Only for speakers */}
                {(isHost || hasMicAccess) && (
                  <Button
                    variant={isMuted ? "secondary" : "destructive"}
                    size="sm"
                    onClick={() => setIsMuted(!isMuted)}
                  >
                    {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  </Button>
                )}

                {/* Request to Speak - Only for guests without mic access */}
                {!isHost && !hasMicAccess && !micRequested && (
                  <Button variant="outline" size="sm" onClick={requestMic}>
                    <Hand className="h-4 w-4 mr-2" />
                    Request to Speak
                  </Button>
                )}

                {/* Reactions */}
                <div className="flex gap-1">
                  {['👏', '❤️', '😂', '🔥', '👍'].map((emoji) => (
                    <Button
                      key={emoji}
                      variant="ghost"
                      size="sm"
                      onClick={() => sendReaction(emoji)}
                      className="text-lg p-1 w-8 h-8 hover:bg-gray-100"
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>

                {/* Chat Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowChat(!showChat)}
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>

                {/* Mic Requests - Only for hosts */}
                {isHost && micRequests.length > 0 && (
                  <Button variant="outline" size="sm" onClick={() => setShowParticipants(true)} className="relative">
                    <Hand className="h-4 w-4 mr-2" />
                    Requests ({micRequests.length})
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  </Button>
                )}

                {/* Host Controls */}
                {isHost ? (
                  <Button variant="destructive" size="sm" onClick={endSpace}>
                    <PhoneOff className="h-4 w-4 mr-2" />
                    End Space
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" onClick={leaveSpace}>
                    <PhoneOff className="h-4 w-4" />
                    Leave
                  </Button>
                )}
              </div>
            </div>
          </div>
          
          {/* Mobile Chat Overlay */}
          {showChat && (
            <div className="absolute inset-0 bg-black/90 flex items-end z-50 md:relative md:w-80 md:border-l md:bg-transparent md:flex-col">
              <div className="bg-gray-900 rounded-t-lg w-full max-h-[70vh] flex flex-col md:bg-background md:rounded-none md:max-h-none md:h-full">
                <div className="flex items-center justify-between p-4 border-b border-gray-700 md:border-gray-200">
                  <h3 className="font-medium text-white md:text-foreground">Chat</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChat(false)}
                    className="text-white hover:bg-gray-800 md:hidden"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <ScrollArea className="flex-1 p-3" ref={chatScrollRef}>
                  <div className="space-y-3">
                    {chatMessages.map((message) => (
                      <div key={message.id} className="text-sm">
                        <div className="flex items-start gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs bg-gray-700 text-white md:bg-gray-200 md:text-gray-900">
                              {message.profile_id.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-xs text-gray-400 md:text-muted-foreground">
                              {message.profile_id.slice(0, 8)}...
                            </p>
                            <p className="text-white md:text-foreground">{message.message}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                <form onSubmit={sendMessage} className="p-3 border-t border-gray-700 md:border-gray-200">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-1 bg-gray-800 border-gray-600 text-white placeholder-gray-400 md:bg-background md:border-border md:text-foreground md:placeholder-muted-foreground"
                    />
                    <Button type="submit" size="sm" disabled={!newMessage.trim()} className="bg-purple-600 hover:bg-purple-700 text-white">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Tip Modal */}
          {showTipModal && (
            <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-50">
              <div className="bg-gray-900 rounded-lg p-6 max-w-sm mx-4 w-full border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Send Tip</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTipModal(false)}
                    className="text-white hover:bg-gray-800"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <p className="text-gray-300 mb-4">
                  Send a tip to {tipTarget?.slice(0, 8)}...
                </p>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  {[1, 5, 10, 25, 50, 100].map((amount) => (
                    <Button
                      key={amount}
                      onClick={() => sendTip(amount)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Custom amount"
                    className="flex-1 bg-gray-800 border-gray-600 text-white"
                  />
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    Send
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
