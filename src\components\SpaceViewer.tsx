import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Mic,
  MicOff,
  Hand,
  MessageSquare,
  Users,
  Settings,
  PhoneOff,
  Send,
  Crown,
  Volume2,
  VolumeX,
  DollarSign,
  X,
  Minimize2
} from 'lucide-react';
import { spaceService, type LiveStream, type StreamParticipant, type StreamChatMessage } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface SpaceViewerProps {
  spaceId: string;
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  currentUserId: string;
}

export const SpaceViewer: React.FC<SpaceViewerProps> = ({
  spaceId,
  isOpen,
  onClose,
  onMinimize,
  currentUserId,
}) => {
  const [space, setSpace] = useState<LiveStream | null>(null);
  const [participants, setParticipants] = useState<StreamParticipant[]>([]);
  const [chatMessages, setChatMessages] = useState<StreamChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [hasMicAccess, setHasMicAccess] = useState(false);
  const [micRequested, setMicRequested] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [micRequests, setMicRequests] = useState<StreamParticipant[]>([]);
  const [reactions, setReactions] = useState<{emoji: string, id: string, timestamp: number}[]>([]);
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipTarget, setTipTarget] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState(false);

  const { toast } = useToast();
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null);

  useEffect(() => {
    if (isOpen && spaceId) {
      loadSpaceData();
      joinSpace();
      
      // Subscribe to real-time updates
      subscriptionRef.current = spaceService.subscribeToStream(spaceId, {
        onParticipantJoined: (participant) => {
          setParticipants(prev => [...prev, participant]);
        },
        onParticipantLeft: (participant) => {
          setParticipants(prev => prev.filter(p => p.id !== participant.id));
        },
        onChatMessage: (message) => {
          setChatMessages(prev => [...prev, message]);
          scrollToBottom();
        },
        onStreamUpdate: (updatedStream) => {
          setSpace(updatedStream);
        },
      });
    }

    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe();
      }
    };
  }, [isOpen, spaceId]);

  useEffect(() => {
    scrollToBottom();
  }, [chatMessages]);

  const loadSpaceData = async () => {
    try {
      // Get space details
      const spaces = await spaceService.getLiveStreams();
      const currentSpace = spaces.find(s => s.id === spaceId);
      if (currentSpace) {
        setSpace(currentSpace);
        setIsHost(currentSpace.host_profile_id === currentUserId);
      }

      // Get participants
      const spaceParticipants = await spaceService.getStreamParticipants(spaceId);
      setParticipants(spaceParticipants);

      // Check if current user is already joined
      const currentUserParticipant = spaceParticipants.find(p => p.profile_id === currentUserId);
      if (currentUserParticipant) {
        setIsJoined(true);
        setHasMicAccess(['host', 'co_host', 'speaker'].includes(currentUserParticipant.role));
      }

      // Get chat messages
      const messages = await spaceService.getChatMessages(spaceId);
      setChatMessages(messages);
    } catch (error) {
      console.error('Error loading space data:', error);
    }
  };

  const joinSpace = async () => {
    if (!isJoined) {
      const success = await spaceService.joinStream(spaceId, currentUserId);
      if (success) {
        setIsJoined(true);
        toast({
          title: "Joined space",
          description: "You're now listening to this space.",
        });
      }
    }
  };

  const leaveSpace = async () => {
    const success = await spaceService.leaveStream(spaceId, currentUserId);
    if (success) {
      setIsJoined(false);
      onClose();
    }
  };

  const endSpace = async () => {
    if (!isHost) return;

    try {
      await spaceService.endStream(spaceId);

      // Save recording to timeline if space was public and recorded
      if (space && space.is_recorded && space.is_public) {
        await saveRecordingToTimeline();
      }

      toast({ title: "Space ended successfully" });
      onClose();
    } catch (error) {
      console.error('Error ending space:', error);
      toast({ title: "Failed to end space", variant: "destructive" });
    }
  };

  const saveRecordingToTimeline = async () => {
    if (!space) return;

    try {
      const duration = Math.floor((Date.now() - new Date(space.created_at).getTime()) / 60000); // minutes

      await supabase
        .from('recorded_spaces')
        .insert({
          space_id: spaceId,
          title: space.title,
          description: space.description,
          host_profile_id: space.host_profile_id,
          duration_minutes: duration,
          participant_count: participants.length,
          is_public: true,
          ended_at: new Date().toISOString()
        });

      console.log('Space recording saved to timeline');
    } catch (error) {
      console.error('Error saving recording to timeline:', error);
    }
  };

  const approveMicRequest = async (requestId: string) => {
    try {
      // Grant mic access to the user
      await spaceService.grantMicAccess(spaceId, requestId);

      // Remove from requests
      setMicRequests(prev => prev.filter(req => req.id !== requestId));

      toast({ title: "Mic access granted" });
    } catch (error) {
      console.error('Error approving mic request:', error);
      toast({ title: "Failed to approve request", variant: "destructive" });
    }
  };

  const denyMicRequest = async (requestId: string) => {
    try {
      // Remove from requests
      setMicRequests(prev => prev.filter(req => req.id !== requestId));

      toast({ title: "Request denied" });
    } catch (error) {
      console.error('Error denying mic request:', error);
    }
  };

  const sendReaction = (emoji: string) => {
    const reactionId = Math.random().toString(36).substring(7);
    const newReaction = {
      emoji,
      id: reactionId,
      timestamp: Date.now()
    };

    setReactions(prev => [...prev, newReaction]);

    // Remove reaction after 3 seconds
    setTimeout(() => {
      setReactions(prev => prev.filter(r => r.id !== reactionId));
    }, 3000);

    // TODO: Send to other participants via real-time
  };

  const openTipModal = (targetUserId: string) => {
    setTipTarget(targetUserId);
    setShowTipModal(true);
  };

  const sendTip = async (amount: number) => {
    if (!tipTarget) return;

    try {
      // TODO: Implement actual tip functionality
      toast({ title: `Sent $${amount} tip!` });
      setShowTipModal(false);
      setTipTarget(null);
    } catch (error) {
      console.error('Error sending tip:', error);
      toast({ title: "Failed to send tip", variant: "destructive" });
    }
  };

  const requestMic = async () => {
    const success = await spaceService.requestMicAccess(spaceId, currentUserId);
    if (success) {
      setMicRequested(true);
      toast({
        title: "Mic request sent",
        description: "The host will review your request.",
      });
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    const success = await spaceService.sendChatMessage(spaceId, currentUserId, newMessage.trim());
    if (success) {
      setNewMessage('');
    } else {
      toast({
        title: "Failed to send message",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const scrollToBottom = () => {
    if (chatScrollRef.current) {
      chatScrollRef.current.scrollTop = chatScrollRef.current.scrollHeight;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'host':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'co_host':
        return <Crown className="h-3 w-3 text-blue-500" />;
      case 'speaker':
        return <Mic className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const speakers = participants.filter(p => ['host', 'co_host', 'speaker'].includes(p.role));
  const listeners = participants.filter(p => p.role === 'listener');

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] h-[600px] p-0">
        <div className="flex h-full">
          {/* Main Space Area */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="p-4 border-b">
              <div className="flex items-center justify-between mb-2">
                <h1 className="text-lg font-semibold">{space?.title}</h1>
                <div className="flex items-center gap-2">
                    🔴 LIVE
                  </Badge>
                  {onMinimize && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={onMinimize}
                      className="hover:bg-gray-100"
                    >
                      <Minimize2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
              {space?.description && (
                <p className="text-sm text-muted-foreground">{space.description}</p>
              )}
              <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  {participants.length} listening
                </span>
                <span className="flex items-center gap-1">
                  <Mic className="h-4 w-4" />
                  {speakers.length} speaking
                </span>
              </div>
            </div>

            {/* Floating Reactions */}
            <div className="absolute top-20 right-4 z-10 pointer-events-none">
              {reactions.map((reaction) => (
                <div
                  key={reaction.id}
                  className="text-2xl animate-bounce mb-2"
                  style={{
                    animation: `float-up 3s ease-out forwards`,
                  }}
                >
                  {reaction.emoji}
                </div>
              ))}
            </div>

            {/* Speakers Section */}
            <div className="flex-1 p-4">
              <h3 className="text-sm font-medium mb-3">On Stage</h3>
              <div className="grid grid-cols-3 gap-4 mb-6">
                {speakers.map((participant) => (
                  <div key={participant.id} className="text-center">
                    <div className="relative mb-2">
                      <Avatar className="w-16 h-16 mx-auto">
                        <AvatarImage src="" />
                        <AvatarFallback>
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -top-1 -right-1">
                        {getRoleIcon(participant.role)}
                      </div>
                      {participant.is_muted ? (
                        <MicOff className="absolute -bottom-1 right-0 h-4 w-4 bg-red-500 text-white rounded-full p-1" />
                      ) : (
                        <Mic className="absolute -bottom-1 right-0 h-4 w-4 bg-green-500 text-white rounded-full p-1" />
                      )}
                    </div>
                    <p className="text-xs truncate mb-1">
                      {participant.profile_id.slice(0, 8)}...
                    </p>
                    {participant.profile_id !== currentUserId && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openTipModal(participant.profile_id)}
                        className="text-xs h-6 px-2"
                      >
                        <DollarSign className="h-3 w-3 mr-1" />
                        Tip
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              {/* Listeners */}
              {listeners.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2">Listeners ({listeners.length})</h3>
                  <div className="flex flex-wrap gap-2">
                    {listeners.slice(0, 20).map((participant) => (
                      <Avatar key={participant.id} className="w-8 h-8">
                        <AvatarImage src="" />
                        <AvatarFallback className="text-xs">
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    ))}
                    {listeners.length > 20 && (
                      <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs">
                        +{listeners.length - 20}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="p-4 border-t">
              <div className="flex items-center justify-center gap-3">
                {/* Mic Control - Only for speakers */}
                {(isHost || hasMicAccess) && (
                  <Button
                    variant={isMuted ? "secondary" : "destructive"}
                    size="sm"
                    onClick={() => setIsMuted(!isMuted)}
                  >
                    {isMuted ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                  </Button>
                )}

                {/* Request to Speak - Only for guests without mic access */}
                {!isHost && !hasMicAccess && !micRequested && (
                  <Button variant="outline" size="sm" onClick={requestMic}>
                    <Hand className="h-4 w-4 mr-2" />
                    Request to Speak
                  </Button>
                )}

                {/* Reactions */}
                <div className="flex gap-1">
                  {['👏', '❤️', '😂', '🔥', '👍'].map((emoji) => (
                    <Button
                      key={emoji}
                      variant="ghost"
                      size="sm"
                      onClick={() => sendReaction(emoji)}
                      className="text-lg p-1 w-8 h-8 hover:bg-gray-100"
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>

                {/* Chat Toggle */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowChat(!showChat)}
                >
                  <MessageSquare className="h-4 w-4" />
                </Button>

                {/* Mic Requests - Only for hosts */}
                {isHost && micRequests.length > 0 && (
                  <Button variant="outline" size="sm" onClick={() => setShowParticipants(true)} className="relative">
                    <Hand className="h-4 w-4 mr-2" />
                    Requests ({micRequests.length})
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  </Button>
                )}

                {/* Host Controls */}
                {isHost ? (
                  <Button variant="destructive" size="sm" onClick={endSpace}>
                    <PhoneOff className="h-4 w-4 mr-2" />
                    End Space
                  </Button>
                ) : (
                  <Button variant="outline" size="sm" onClick={leaveSpace}>
                    <PhoneOff className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Mobile Chat Overlay */}
          {showChat && (
            <div className="absolute inset-0 bg-black/90 flex items-end z-50 md:relative md:w-80 md:border-l md:bg-transparent md:flex-col">
              <div className="bg-gray-900 rounded-t-lg w-full max-h-[70vh] flex flex-col md:bg-background md:rounded-none md:max-h-none md:h-full">
                <div className="flex items-center justify-between p-4 border-b border-gray-700 md:border-gray-200">
                  <h3 className="font-medium text-white md:text-foreground">Chat</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChat(false)}
                    className="text-white hover:bg-gray-800 md:hidden"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <ScrollArea className="flex-1 p-3" ref={chatScrollRef}>
                  <div className="space-y-3">
                    {chatMessages.map((message) => (
                      <div key={message.id} className="text-sm">
                        <div className="flex items-start gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs bg-gray-700 text-white md:bg-gray-200 md:text-gray-900">
                              {message.profile_id.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="text-xs text-gray-400 md:text-muted-foreground">
                              {message.profile_id.slice(0, 8)}...
                            </p>
                            <p className="text-white md:text-foreground">{message.message}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>

                <form onSubmit={sendMessage} className="p-3 border-t border-gray-700 md:border-gray-200">
                  <div className="flex gap-2">
                    <Input
                      placeholder="Type a message..."
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      className="flex-1 bg-gray-800 border-gray-600 text-white placeholder-gray-400 md:bg-background md:border-border md:text-foreground md:placeholder-muted-foreground"
                    />
                    <Button type="submit" size="sm" disabled={!newMessage.trim()} className="bg-purple-600 hover:bg-purple-700 text-white">
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          )}

          {/* Mic Requests Modal - For Hosts */}
          {showParticipants && isHost && (
            <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-50">
              <div className="bg-gray-900 rounded-lg p-6 max-w-md mx-4 w-full border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Mic Requests</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowParticipants(false)}
                    className="text-white hover:bg-gray-800"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {micRequests.length === 0 ? (
                    <p className="text-gray-400 text-center py-4">No pending requests</p>
                  ) : (
                    micRequests.map((request) => (
                      <div key={request.id} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="bg-gray-700 text-white text-xs">
                              {request.profile_id.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-white text-sm">
                            {request.profile_id.slice(0, 8)}...
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => approveMicRequest(request.id)}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            Approve
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => denyMicRequest(request.id)}
                            className="border-gray-600 text-white hover:bg-gray-800"
                          >
                            Deny
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Tip Modal */}
          {showTipModal && (
            <div className="absolute inset-0 bg-black/90 flex items-center justify-center z-50">
              <div className="bg-gray-900 rounded-lg p-6 max-w-sm mx-4 w-full border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-white">Send Tip</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTipModal(false)}
                    className="text-white hover:bg-gray-800"
                  >
                    <X className="h-5 w-5" />
                  </Button>
                </div>

                <p className="text-gray-300 mb-4">
                  Send a tip to {tipTarget?.slice(0, 8)}...
                </p>

                <div className="grid grid-cols-3 gap-3 mb-4">
                  {[1, 5, 10, 25, 50, 100].map((amount) => (
                    <Button
                      key={amount}
                      onClick={() => sendTip(amount)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      ${amount}
                    </Button>
                  ))}
                </div>

                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Custom amount"
                    className="flex-1 bg-gray-800 border-gray-600 text-white"
                  />
                  <Button className="bg-green-600 hover:bg-green-700 text-white">
                    Send
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};