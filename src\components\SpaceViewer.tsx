import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { 
  Mi<PERSON>, 
  MicOff, 
  Hand, 
  MessageSquare, 
  Users, 
  Settings, 
  PhoneOff,
  Send,
  Crown,
  Volume2,
  VolumeX,
  DollarSign,
  X,
  Minimize2
} from 'lucide-react';
import { spaceService, type LiveStream, type StreamParticipant, type StreamChatMessage } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

interface SpaceViewerProps {
  spaceId: string;
  isOpen: boolean;
  onClose: () => void;
  onMinimize?: () => void;
  currentUserId: string;
}

export const SpaceViewer: React.FC<SpaceViewerProps> = ({
  spaceId,
  isOpen,
  onClose,
  onMinimize,
  currentUserId,
}) => {
  const [profileId, setProfileId] = useState<string | null>(null);
  const [space, setSpace] = useState<LiveStream | null>(null);
  const [participants, setParticipants] = useState<StreamParticipant[]>([]);
  const [chatMessages, setChatMessages] = useState<StreamChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isJoined, setIsJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(true);
  const [isSelfMuted, setIsSelfMuted] = useState(false);
  const [hasMicAccess, setHasMicAccess] = useState(false);
  const [micRequested, setMicRequested] = useState(false);
  const [showChat, setShowChat] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [micRequests, setMicRequests] = useState<StreamParticipant[]>([]);
  const [reactions, setReactions] = useState<{emoji: string, id: string, timestamp: number, userId?: string}[]>([]);
  const [showTipModal, setShowTipModal] = useState(false);
  const [tipTarget, setTipTarget] = useState<string | null>(null);
  const [showParticipants, setShowParticipants] = useState(false);
  const [spaceSettings, setSpaceSettings] = useState<any>({});
  const [isCoHost, setIsCoHost] = useState(false);
  const [showHostControls, setShowHostControls] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const chatScrollRef = useRef<HTMLDivElement>(null);
  const subscriptionRef = useRef<{ unsubscribe: () => void } | null>(null);

  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && spaceId && currentUserId) {
      console.log('🚀 SpaceViewer: Starting with:', { spaceId, currentUserId });
      getProfileId();
    }
  }, [isOpen, spaceId, currentUserId]);

  useEffect(() => {
    if (profileId && spaceId) {
      loadSpaceData();
      loadMicRequests(); // Load mic requests for hosts
    }
  }, [profileId, spaceId]);

  // Real-time updates for mic requests and participant changes
  useEffect(() => {
    if (!spaceId) return;

    const loadUpdatesInterval = setInterval(() => {
      loadParticipants();
      if (isHost) {
        loadMicRequests();
      }
      
      // Check if current user got mic access
      if (micRequested && !hasMicAccess) {
        checkMicStatus();
      }
    }, 2000); // Check every 2 seconds

    return () => clearInterval(loadUpdatesInterval);
  }, [isHost, spaceId, micRequested, hasMicAccess]);

  const getProfileId = async () => {
    try {
      console.log('🔍 SpaceViewer: Getting profile ID for currentUserId:', currentUserId);
      console.log('🔍 SpaceViewer: currentUserId length:', currentUserId.length);
      console.log('🔍 SpaceViewer: currentUserId includes dash:', currentUserId.includes('-'));

      // Check if it's a UUID pattern (8-4-4-4-12 format)
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
      const isUUID = uuidPattern.test(currentUserId);

      console.log('🔍 SpaceViewer: UUID pattern test:', isUUID);

      // If currentUserId is already a UUID (not a wallet address), use it directly
      if (isUUID) {
        console.log('✅ SpaceViewer: currentUserId is already a UUID:', currentUserId);
        setProfileId(currentUserId);
        return;
      }

      // Otherwise, get profile ID from wallet address
      console.log('🔍 SpaceViewer: Looking up wallet address:', currentUserId.toLowerCase());
      const { data: profileData, error } = await supabase
        .from('profiles')
        .select('id, wallet_address')
        .eq('wallet_address', currentUserId.toLowerCase())
        .single();

      console.log('📊 SpaceViewer: Profile lookup result:', { profileData, error });

      if (error) {
        console.error('❌ SpaceViewer: Error fetching profile:', error);

        // Try case-insensitive search as fallback
        console.log('🔄 SpaceViewer: Trying case-insensitive search...');
        const { data: fallbackData, error: fallbackError } = await supabase
          .from('profiles')
          .select('id, wallet_address')
          .ilike('wallet_address', currentUserId)
          .single();

        console.log('📊 SpaceViewer: Fallback lookup result:', { fallbackData, fallbackError });

        if (fallbackData) {
          console.log('✅ SpaceViewer: Found profile with fallback search:', fallbackData.id);
          setProfileId(fallbackData.id);
          return;
        }

        toast({ title: "Failed to load profile", variant: "destructive" });
        return;
      }

      if (profileData) {
        console.log('✅ SpaceViewer: Found profile ID:', profileData.id);
        setProfileId(profileData.id);
      } else {
        console.log('❌ SpaceViewer: No profile found for wallet address');
        toast({ title: "Profile not found", variant: "destructive" });
      }
    } catch (error) {
      console.error('❌ SpaceViewer: Error getting profile ID:', error);
      toast({ title: "Failed to load profile", variant: "destructive" });
    }
  };

  const loadSpaceData = async () => {
    try {
      // Get all live streams and find the one we need
      const allStreams = await spaceService.getLiveStreams();
      const spaceData = allStreams.find(stream => stream.id === spaceId);

      if (spaceData && profileId) {
        setSpace(spaceData);

        // Load space settings
        const settings = spaceData.settings || {};
        setSpaceSettings(settings);
        console.log('⚙️ SpaceViewer: Space settings:', settings);

        // Ensure both IDs are strings and compare them
        const hostId = String(spaceData.host_profile_id).trim();
        const currentId = String(profileId).trim();
        const isUserHost = hostId === currentId;

        console.log('🎯 SpaceViewer: Checking host status:', {
          spaceHostId: hostId,
          currentProfileId: currentId,
          isUserHost,
          exactMatch: hostId === currentId,
          spaceHostIdType: typeof spaceData.host_profile_id,
          profileIdType: typeof profileId,
          spaceHostIdLength: hostId.length,
          profileIdLength: currentId.length
        });
        setIsHost(isUserHost);

        // Auto-join the space - hosts get automatically joined as 'host', others as 'listener'
        const joinSuccess = await spaceService.joinStream(spaceId, profileId, isUserHost ? 'host' : 'listener');
        setIsJoined(joinSuccess);

        // Load participants and messages after joining
        await loadParticipants();
        await loadChatMessages();

        // Set mic access and co-host status based on role after loading participants
        if (joinSuccess) {
          // Get fresh participant data instead of relying on state
          const freshParticipants = await spaceService.getStreamParticipants(spaceId);
          const currentUserParticipant = freshParticipants.find(p => p.profile_id === profileId);

          console.log('🎯 SpaceViewer: Current user participant:', currentUserParticipant);
          console.log('🎯 SpaceViewer: All participants:', freshParticipants);
          console.log('🎯 SpaceViewer: Looking for profileId:', profileId);

          if (currentUserParticipant) {
            const hasAccess = ['host', 'co_host', 'speaker'].includes(currentUserParticipant.role);
            const isCoHostRole = currentUserParticipant.role === 'co_host';
            const isHostRole = currentUserParticipant.role === 'host';

            console.log('🎯 SpaceViewer: Setting access:', {
              role: currentUserParticipant.role,
              hasAccess,
              isCoHostRole,
              isHostRole
            });

            setHasMicAccess(hasAccess);
            setIsCoHost(isCoHostRole);
            setIsSelfMuted(currentUserParticipant.is_muted);

            // Force host status if user has 'host' role in participants table
            if (isHostRole && !isUserHost) {
              console.log('🔧 SpaceViewer: Forcing host status based on participant role');
              setIsHost(true);
            }
          } else if (isUserHost) {
            // Host should always have mic access
            console.log('🎯 SpaceViewer: Setting host access (fallback)');
            setHasMicAccess(true);
            setIsCoHost(false);
          }
        }
      }
    } catch (error) {
      console.error('Error loading space data:', error);
      toast({ title: "Failed to load space", variant: "destructive" });
    }
  };

  const loadParticipants = async () => {
    try {
      const participantsList = await spaceService.getStreamParticipants(spaceId);
      setParticipants(participantsList);

      // Update co-host status, mic access, and mute status for current user
      if (profileId) {
        const currentUserParticipant = participantsList.find(p => p.profile_id === profileId);
        if (currentUserParticipant) {
          setIsCoHost(currentUserParticipant.role === 'co_host');
          setHasMicAccess(['host', 'co_host', 'speaker'].includes(currentUserParticipant.role));
          setIsSelfMuted(currentUserParticipant.is_muted);
        }
      }
    } catch (error) {
      console.error('Error loading participants:', error);
    }
  };

  const loadChatMessages = async () => {
    try {
      const messages = await spaceService.getChatMessages(spaceId);
      setChatMessages(messages);
    } catch (error) {
      console.error('Error loading chat messages:', error);
    }
  };

  const loadMicRequests = async () => {
    if (!isHost) return;
    
    try {
      const participantsList = await spaceService.getStreamParticipants(spaceId);
      const requests = participantsList.filter(p => 
        p.mic_requested_at && !p.mic_granted_at && p.role === 'listener'
      );
      setMicRequests(requests);
    } catch (error) {
      console.error('Error loading mic requests:', error);
    }
  };

  const checkMicStatus = async () => {
    if (!profileId) return;
    
    try {
      const participantsList = await spaceService.getStreamParticipants(spaceId);
      const currentUser = participantsList.find(p => p.profile_id === profileId);
      
      if (currentUser) {
        const hasAccess = ['host', 'co_host', 'speaker'].includes(currentUser.role) || currentUser.mic_granted_at !== null;
        setHasMicAccess(hasAccess);
        
        if (hasAccess && micRequested) {
          setMicRequested(false);
          toast({ title: "Mic access granted! You can now speak." });
        }
      }
    } catch (error) {
      console.error('Error checking mic status:', error);
    }
  };

  // Helper function for granting mic access (Host only)
  const grantMicAccess = async (participantId: string) => {
    if (!isHost || !profileId) return;

    try {
      const success = await spaceService.grantMicAccess(spaceId, participantId, profileId);
      if (success) {
        toast({ title: "Mic access granted" });
        await loadParticipants(); // Refresh participants
        // Remove from mic requests
        setMicRequests(prev => prev.filter(r => r.profile_id !== participantId));
      } else {
        toast({ title: "Failed to grant mic access", variant: "destructive" });
      }
    } catch (error) {
      console.error('Error granting mic access:', error);
      toast({ title: "Failed to grant mic access", variant: "destructive" });
    }
  };

  // Helper function for muting/unmuting participants (Host/Co-host only)
  const toggleParticipantMute = async (participantId: string, currentMuteState: boolean) => {
    if (!isHost && !isCoHost) return;

    try {
      const { error } = await supabase
        .from('stream_participants')
        .update({ is_muted: !currentMuteState })
        .eq('stream_id', spaceId)
        .eq('profile_id', participantId);

      if (error) throw error;

      toast({
        title: !currentMuteState ? "Participant muted" : "Participant unmuted"
      });
      await loadParticipants(); // Refresh participants
    } catch (error) {
      console.error('Error toggling participant mute:', error);
      toast({ title: "Failed to update mute status", variant: "destructive" });
    }
  };

  // Mute all listeners (Host only)
  const muteAllListeners = async () => {
    if (!isHost) return;

    try {
      const { error } = await supabase
        .from('stream_participants')
        .update({ is_muted: true })
        .eq('stream_id', spaceId)
        .eq('role', 'listener');

      if (error) throw error;

      toast({ title: "All listeners muted" });
      await loadParticipants();
    } catch (error) {
      console.error('Error muting all listeners:', error);
      toast({ title: "Failed to mute all listeners", variant: "destructive" });
    }
  };

  // Unmute all listeners (Host only)
  const unmuteAllListeners = async () => {
    if (!isHost) return;

    try {
      const { error } = await supabase
        .from('stream_participants')
        .update({ is_muted: false })
        .eq('stream_id', spaceId)
        .eq('role', 'listener');

      if (error) throw error;

      toast({ title: "All listeners unmuted" });
      await loadParticipants();
    } catch (error) {
      console.error('Error unmuting all listeners:', error);
      toast({ title: "Failed to unmute all listeners", variant: "destructive" });
    }
  };

  // Promote user to co-host (Host only)
  const promoteToCoHost = async (participantId: string) => {
    if (!isHost) return;

    try {
      const { error } = await supabase
        .from('stream_participants')
        .update({ role: 'co_host' })
        .eq('stream_id', spaceId)
        .eq('profile_id', participantId);

      if (error) throw error;

      toast({ title: "User promoted to co-host" });
      await loadParticipants();
    } catch (error) {
      console.error('Error promoting to co-host:', error);
      toast({ title: "Failed to promote user", variant: "destructive" });
    }
  };

  // Demote co-host to listener (Host only)
  const demoteCoHost = async (participantId: string) => {
    if (!isHost) return;

    try {
      const { error } = await supabase
        .from('stream_participants')
        .update({ role: 'listener' })
        .eq('stream_id', spaceId)
        .eq('profile_id', participantId);

      if (error) throw error;

      toast({ title: "Co-host demoted to listener" });
      await loadParticipants();
    } catch (error) {
      console.error('Error demoting co-host:', error);
      toast({ title: "Failed to demote co-host", variant: "destructive" });
    }
  };

  // Toggle self-mute for speakers
  const toggleSelfMute = async () => {
    if (!profileId || !hasMicAccess) return;

    try {
      const newMuteState = !isSelfMuted;

      const { error } = await supabase
        .from('stream_participants')
        .update({ is_muted: newMuteState })
        .eq('stream_id', spaceId)
        .eq('profile_id', profileId);

      if (error) throw error;

      setIsSelfMuted(newMuteState);
      toast({
        title: newMuteState ? "You muted yourself" : "You unmuted yourself"
      });
      await loadParticipants();
    } catch (error) {
      console.error('Error toggling self mute:', error);
      toast({ title: "Failed to toggle mute", variant: "destructive" });
    }
  };

  // Toggle screen sharing
  const toggleScreenShare = async () => {
    if (!spaceSettings.allowScreenShare) {
      toast({
        title: "Screen sharing not allowed",
        description: "The host has not enabled screen sharing for this space.",
        variant: "destructive"
      });
      return;
    }

    try {
      if (!isScreenSharing) {
        // Start screen sharing
        const stream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true
        });

        setIsScreenSharing(true);
        toast({ title: "Screen sharing started" });

        // Handle stream end
        stream.getVideoTracks()[0].onended = () => {
          setIsScreenSharing(false);
          toast({ title: "Screen sharing stopped" });
        };
      } else {
        // Stop screen sharing
        setIsScreenSharing(false);
        toast({ title: "Screen sharing stopped" });
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
      toast({
        title: "Screen sharing failed",
        description: "Could not access screen sharing.",
        variant: "destructive"
      });
    }
  };

  const leaveSpace = async () => {
    if (!profileId) {
      console.error('No profile ID available for leaving space');
      return;
    }

    console.log('Leaving space:', spaceId, 'with profile:', profileId);

    try {
      const success = await spaceService.leaveStream(spaceId, profileId);
      if (success) {
        setIsJoined(false);
        toast({ title: "Left space successfully" });
        onClose();
      } else {
        toast({ title: "Failed to leave space", variant: "destructive" });
      }
    } catch (error) {
      console.error('Error leaving space:', error);
      toast({ title: "Failed to leave space", variant: "destructive" });
    }
  };

  const endSpace = async () => {
    if (!isHost) return;

    try {
      // End the stream
      const success = await spaceService.endStream(spaceId);
      if (success) {
        toast({ title: "Space ended successfully" });
        
        // Cleanup: leave all participants and close
        await leaveSpace();
        
        // Force a refresh of live spaces by triggering a global event
        window.dispatchEvent(new CustomEvent('spaceEnded', { detail: { spaceId } }));
        
        onClose();
      } else {
        throw new Error('Failed to end stream');
      }
    } catch (error) {
      console.error('Error ending space:', error);
      toast({ title: "Failed to end space", variant: "destructive" });
    }
  };

  const requestMic = async () => {
    if (!profileId) {
      console.error('No profile ID available for mic request');
      return;
    }

    console.log('Requesting mic access for profile:', profileId, 'in space:', spaceId);

    try {
      const success = await spaceService.requestMicAccess(spaceId, profileId);
      if (success) {
        setMicRequested(true);
        toast({
          title: "Mic request sent",
          description: "The host will review your request.",
        });
      } else {
        toast({
          title: "Failed to request mic access",
          description: "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error requesting mic access:', error);
      toast({
        title: "Failed to request mic access",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !profileId) return;

    console.log('Sending message:', newMessage.trim(), 'from profile:', profileId, 'to space:', spaceId);

    try {
      // Ensure user is joined to the space first
      if (!isJoined) {
        const joinSuccess = await spaceService.joinStream(spaceId, profileId);
        if (joinSuccess) {
          setIsJoined(true);
        } else {
          toast({
            title: "Failed to join space",
            description: "Cannot send message without joining.",
            variant: "destructive",
          });
          return;
        }
      }

      const success = await spaceService.sendChatMessage(spaceId, profileId, newMessage.trim());
      if (success) {
        setNewMessage('');
        toast({ title: "Message sent!" });
      } else {
        toast({
          title: "Failed to send message",
          description: "Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: "Failed to send message",
        description: "Please try again.",
        variant: "destructive",
      });
    }
  };

  const sendReaction = (emoji: string) => {
    if (!profileId) return;

    const reactionId = Math.random().toString(36).substring(7);
    const newReaction = {
      emoji,
      id: reactionId,
      timestamp: Date.now(),
      userId: profileId
    };

    setReactions(prev => [...prev, newReaction]);

    // TODO: Send reaction to other participants via real-time
    // This should broadcast to all space participants
    console.log(`${profileId} sent reaction: ${emoji}`);

    // Remove reaction after 5 seconds (longer for better visibility)
    setTimeout(() => {
      setReactions(prev => prev.filter(r => r.id !== reactionId));
    }, 5000);
  };

  const openTipModal = (targetUserId: string) => {
    setTipTarget(targetUserId);
    setShowTipModal(true);
  };

  const sendTip = async (amount: number) => {
    if (!tipTarget || !spaceSettings.enableTipping) return;

    try {
      // Check if tipping is enabled for this space
      if (!spaceSettings.enableTipping) {
        toast({
          title: "Tipping not enabled",
          description: "The host has not enabled tipping for this space.",
          variant: "destructive"
        });
        return;
      }

      // TODO: Implement actual blockchain tip functionality
      console.log(`Sending $${amount} tip to ${tipTarget}`);

      // For now, just show success message
      toast({
        title: `Sent $${amount} tip!`,
        description: "Tip sent successfully via blockchain."
      });

      setShowTipModal(false);
      setTipTarget(null);
    } catch (error) {
      console.error('Error sending tip:', error);
      toast({ title: "Failed to send tip", variant: "destructive" });
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'host':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'co_host':
        return <Crown className="h-3 w-3 text-blue-500" />;
      case 'speaker':
        return <Mic className="h-3 w-3 text-green-500" />;
      default:
        return null;
    }
  };

  const speakers = participants.filter(p => ['host', 'co_host', 'speaker'].includes(p.role));
  const listeners = participants.filter(p => p.role === 'listener');

  if (!isOpen) return null;

  if (!profileId) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[400px] h-[200px] p-6">
          <DialogTitle className="sr-only">Loading Space</DialogTitle>
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-sm text-muted-foreground">Loading profile...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] sm:max-w-[800px] h-[90vh] sm:h-[600px] p-0 overflow-hidden">
        <DialogTitle className="sr-only">
          {space?.title || 'Space Viewer'}
        </DialogTitle>
        <div className="flex flex-col sm:flex-row h-full overflow-hidden">
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b flex-shrink-0">
              <div className="flex items-center justify-between">
                <h1 className="text-lg font-semibold">{space?.title || 'Loading...'}</h1>
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="animate-pulse">
                    🔴 LIVE
                  </Badge>
                  {isHost && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                      👑 HOST
                    </Badge>
                  )}
                  {isCoHost && (
                    <Badge variant="outline" className="text-blue-600 border-blue-600">
                      👑 CO-HOST
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            
            {/* Space Description */}
            {space?.description && (
              <div className="px-4 pb-2">
                <p className="text-sm text-muted-foreground">{space.description}</p>
              </div>
            )}

            {/* Stats */}
            <div className="px-4 pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    {participants.length} listening
                  </span>
                  <span className="flex items-center gap-1">
                    <Mic className="h-4 w-4" />
                    {speakers.length} speaking
                  </span>
                </div>

                {/* Join Status */}
                {isJoined && (
                  <Badge variant="secondary" className="text-xs">
                    ✓ Joined
                  </Badge>
                )}
              </div>
            </div>

            {/* Floating Reactions */}
            <div className="absolute inset-0 pointer-events-none z-20">
              {reactions.map((reaction) => (
                <div
                  key={reaction.id}
                  className="absolute animate-float-up"
                  style={{
                    left: `${Math.random() * 80 + 10}%`,
                    top: `${Math.random() * 60 + 20}%`,
                    animation: `float-up 5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards`,
                  }}
                >
                  <div className="bg-black/80 text-white rounded-full px-3 py-2 flex items-center gap-2 shadow-lg">
                    <span className="text-3xl">{reaction.emoji}</span>
                    <span className="text-xs font-medium">
                      {reaction.userId === profileId ? 'You' : reaction.userId?.slice(0, 6)}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Speakers Section */}
            <div className="flex-1 overflow-hidden flex flex-col">
              <div className="p-4 flex-shrink-0">
                <h3 className="text-sm font-medium mb-3">On Stage</h3>
              </div>
              <ScrollArea className="flex-1 px-4">
                <div className="grid grid-cols-3 gap-4 mb-6">
                {speakers.map((participant) => (
                  <div key={participant.id} className="text-center">
                    <div className="relative mb-2">
                      <Avatar className="w-16 h-16 mx-auto">
                        <AvatarFallback>
                          {participant.profile_id.slice(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -top-1 -right-1">
                        {getRoleIcon(participant.role)}
                      </div>
                      {participant.is_muted ? (
                        <MicOff className="absolute -bottom-1 right-0 h-4 w-4 bg-red-500 text-white rounded-full p-1" />
                      ) : (
                        <Mic className="absolute -bottom-1 right-0 h-4 w-4 bg-green-500 text-white rounded-full p-1" />
                      )}
                    </div>
                    <p className="text-xs truncate mb-1">
                      {participant.profile_id === profileId ? 'You' : participant.profile_id.slice(0, 8) + '...'}
                    </p>
                    <div className="flex gap-1 justify-center">
                      {participant.profile_id !== profileId && (
                        <>
                          {/* Tip button (only if tipping is enabled) */}
                          {spaceSettings.enableTipping && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openTipModal(participant.profile_id)}
                              className="text-xs h-6 px-2"
                            >
                              <DollarSign className="h-3 w-3 mr-1" />
                              Tip
                            </Button>
                          )}
                          {isHost && (
                            <>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => toggleParticipantMute(participant.profile_id, participant.is_muted)}
                                className="text-xs h-6 px-2"
                              >
                                {participant.is_muted ? <Mic className="h-3 w-3" /> : <MicOff className="h-3 w-3" />}
                              </Button>

                              {/* Co-host promotion for speakers */}
                              {participant.role === 'speaker' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => promoteToCoHost(participant.profile_id)}
                                  className="text-xs h-6 px-2 bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
                                  title="Promote to Co-Host"
                                >
                                  👑
                                </Button>
                              )}

                              {participant.role === 'co_host' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => demoteCoHost(participant.profile_id)}
                                  className="text-xs h-6 px-2 bg-red-600 text-white border-red-600 hover:bg-red-700"
                                  title="Remove Co-Host"
                                >
                                  ✗
                                </Button>
                              )}
                            </>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>

                {/* Listeners */}
                {listeners.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium mb-2">Listeners ({listeners.length})</h3>
                    <div className="flex flex-wrap gap-2">
                      {listeners.slice(0, 20).map((participant) => (
                        <Avatar key={participant.id} className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {participant.profile_id.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                      ))}
                      {listeners.length > 20 && (
                        <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs">
                          +{listeners.length - 20}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </ScrollArea>
            </div>
            
            {/* Controls */}
            <div className="p-4 border-t bg-background">
              <div className="flex flex-wrap items-center justify-center gap-2 sm:gap-3">
                {/* Host Controls */}
                {isHost ? (
                  <>
                    {/* Mic Control for Host */}
                    <Button
                      variant={isSelfMuted ? "secondary" : "destructive"}
                      size="lg"
                      onClick={toggleSelfMute}
                      className="w-12 h-12 rounded-full flex-shrink-0 flex items-center justify-center"
                    >
                      {isSelfMuted ? (
                        <MicOff className="h-5 w-5 text-white" />
                      ) : (
                        <Mic className="h-5 w-5 text-white" />
                      )}
                    </Button>

                    {/* Mic Requests for Host */}
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setShowParticipants(!showParticipants)}
                      className="relative flex-shrink-0"
                    >
                      <Hand className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">Requests</span>
                      {micRequests.length > 0 && (
                        <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 rounded-full flex items-center justify-center">
                          {micRequests.length}
                        </Badge>
                      )}
                    </Button>

                    {/* Host Controls Menu */}
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={() => setShowHostControls(!showHostControls)}
                      className="flex-shrink-0"
                      title="Space Settings & Features"
                    >
                      <Settings className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">Settings</span>
                    </Button>

                    {/* Mute All / Unmute All */}
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={muteAllListeners}
                      className="flex-shrink-0"
                    >
                      <MicOff className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">Mute All</span>
                    </Button>

                    <Button
                      variant="outline"
                      size="lg"
                      onClick={unmuteAllListeners}
                      className="flex-shrink-0"
                    >
                      <Mic className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">Unmute All</span>
                    </Button>

                    {/* Screen Share (if enabled) */}
                    {spaceSettings.allowScreenShare && (
                      <Button
                        variant={isScreenSharing ? "default" : "outline"}
                        size="lg"
                        onClick={toggleScreenShare}
                        className="flex-shrink-0"
                      >
                        <Settings className="h-5 w-5 mr-2" />
                        <span className="hidden sm:inline">
                          {isScreenSharing ? "Stop Share" : "Share Screen"}
                        </span>
                      </Button>
                    )}

                    {/* End Space for Host */}
                    <Button
                      variant="destructive"
                      size="lg"
                      onClick={endSpace}
                      className="flex-shrink-0"
                    >
                      <PhoneOff className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">End Space</span>
                    </Button>
                  </>
                ) : (
                  <>
                    {/* Guest Controls */}
                    {hasMicAccess ? (
                      <>
                        {/* Mic Control for Speakers/Co-hosts */}
                        <Button
                          variant={isSelfMuted ? "secondary" : "destructive"}
                          size="lg"
                          onClick={toggleSelfMute}
                          className="w-12 h-12 rounded-full flex-shrink-0 flex items-center justify-center"
                        >
                          {isSelfMuted ? (
                            <MicOff className="h-5 w-5 text-white" />
                          ) : (
                            <Mic className="h-5 w-5 text-white" />
                          )}
                        </Button>

                        {/* Screen Share for Speakers */}
                        {spaceSettings.allowScreenShare && (
                          <Button
                            variant={isScreenSharing ? "default" : "outline"}
                            size="lg"
                            onClick={toggleScreenShare}
                            className="flex-shrink-0"
                          >
                            <Settings className="h-5 w-5 mr-2" />
                            <span className="hidden sm:inline">
                              {isScreenSharing ? "Stop" : "Share"}
                            </span>
                          </Button>
                        )}

                        {/* Co-host controls */}
                        {isCoHost && (
                          <Button
                            variant="outline"
                            size="lg"
                            onClick={() => setShowParticipants(!showParticipants)}
                            className="relative flex-shrink-0"
                          >
                            <Hand className="h-5 w-5 mr-2" />
                            <span className="hidden sm:inline">Manage</span>
                            {micRequests.length > 0 && (
                              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 rounded-full flex items-center justify-center">
                                {micRequests.length}
                              </Badge>
                            )}
                          </Button>
                        )}
                      </>
                    ) : (
                      /* Request Mic for Listeners */
                      <Button
                        variant={micRequested ? "secondary" : "outline"}
                        size="lg"
                        onClick={requestMic}
                        disabled={micRequested}
                        className="relative flex-shrink-0"
                      >
                        <Hand className="h-5 w-5 mr-2" />
                        <span className="hidden sm:inline">{micRequested ? "Request Sent" : "Request Mic"}</span>
                        <span className="sm:hidden">{micRequested ? "Sent" : "Mic"}</span>
                        {micRequested && (
                          <Badge className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs">
                            ⏳
                          </Badge>
                        )}
                      </Button>
                    )}

                    {/* Leave Space for Guests */}
                    <Button
                      variant="outline"
                      size="lg"
                      onClick={leaveSpace}
                      className="flex-shrink-0"
                    >
                      <PhoneOff className="h-5 w-5 mr-2" />
                      <span className="hidden sm:inline">Leave Space</span>
                    </Button>
                  </>
                )}

                {/* Reactions - Available for everyone */}
                <div className="flex gap-1 flex-shrink-0">
                  {['👏', '❤️', '😂', '🔥', '🤯'].map((emoji) => (
                    <Button
                      key={emoji}
                      variant="ghost"
                      size="sm"
                      onClick={() => sendReaction(emoji)}
                      className="text-lg p-1 w-8 h-8"
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>

                {/* Chat Toggle */}
                <Button
                  variant="ghost"
                  size="lg"
                  onClick={() => setShowChat(!showChat)}
                  className="w-12 h-12 rounded-full flex-shrink-0"
                >
                  <MessageSquare className="h-5 w-5" />
                </Button>

                {/* Minimize Button */}
                {onMinimize && (
                  <Button
                    variant="ghost"
                    size="lg"
                    onClick={onMinimize}
                    className="w-12 h-12 rounded-full flex-shrink-0"
                  >
                    <Minimize2 className="h-5 w-5" />
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Host Controls Panel */}
          {showHostControls && isHost && (
            <div className="w-80 border-l border-border bg-background flex flex-col">
              <div className="p-4 border-b border-border bg-muted/10">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-foreground">Space Settings</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowHostControls(false)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <ScrollArea className="flex-1">
                <div className="p-4 space-y-4">
                  {/* Space Features */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-sm">Active Features</h4>

                    {spaceSettings.enableTipping && (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <DollarSign className="h-4 w-4" />
                        Tipping Enabled
                      </div>
                    )}

                    {spaceSettings.allowScreenShare && (
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <Settings className="h-4 w-4" />
                        Screen Sharing Allowed
                      </div>
                    )}

                    {spaceSettings.requireApproval && (
                      <div className="flex items-center gap-2 text-sm text-orange-600">
                        <Hand className="h-4 w-4" />
                        Host Approval Required
                      </div>
                    )}

                    {spaceSettings.requireVerification && (
                      <div className="flex items-center gap-2 text-sm text-purple-600">
                        <Crown className="h-4 w-4" />
                        Verification Required
                      </div>
                    )}

                    {space?.is_recorded && (
                      <div className="flex items-center gap-2 text-sm text-red-600">
                        <Settings className="h-4 w-4" />
                        Recording Active
                      </div>
                    )}
                  </div>

                  {/* Moderation Settings */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-sm">Moderation</h4>

                    {spaceSettings.autoModeration && (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <Settings className="h-4 w-4" />
                        Auto Moderation On
                      </div>
                    )}

                    {spaceSettings.profanityFilter && (
                      <div className="flex items-center gap-2 text-sm text-blue-600">
                        <Settings className="h-4 w-4" />
                        Profanity Filter On
                      </div>
                    )}
                  </div>
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Participants Panel (Host/Co-host Only) */}
          {(isHost || isCoHost) && showParticipants && (
            <div className="w-80 border-l border-border bg-background">
              <div className="p-4 border-b border-border bg-muted/10">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-foreground">Host Panel</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowParticipants(false)}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <ScrollArea className="h-[400px] p-4">
                {/* Mic Requests */}
                {micRequests.length > 0 && (
                  <div className="mb-6">
                    <h4 className="font-medium mb-3 text-sm text-orange-600">
                      Mic Requests ({micRequests.length})
                    </h4>
                    <div className="space-y-2">
                      {micRequests.map((request) => (
                        <div key={request.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border shadow-sm">
                          <div className="flex items-center gap-3">
                            <Avatar className="w-8 h-8">
                              <AvatarFallback className="text-xs bg-primary/10 text-primary">
                                {request.profile_id.slice(0, 2).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            <span className="text-sm font-medium text-foreground">
                              {request.profile_id.slice(0, 8)}...
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => grantMicAccess(request.profile_id)}
                              className="h-7 px-3 text-xs bg-green-600 text-white border-green-600 hover:bg-green-700"
                            >
                              ✓ Accept
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 px-3 text-xs text-muted-foreground hover:text-foreground hover:bg-muted"
                            >
                              ✗ Deny
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* All Participants */}
                <div>
                  <h4 className="font-medium mb-3 text-sm">
                    All Participants ({participants.length})
                  </h4>
                  <div className="space-y-2">
                    {participants.map((participant) => (
                      <div key={participant.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border shadow-sm">
                        <div className="flex items-center gap-3">
                          <Avatar className="w-8 h-8">
                            <AvatarFallback className="text-xs bg-primary/10 text-primary">
                              {participant.profile_id.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <span className="text-sm font-medium text-foreground">
                              {participant.profile_id === profileId ? 'You' : participant.profile_id.slice(0, 8) + '...'}
                            </span>
                            <div className="flex items-center gap-1">
                              {getRoleIcon(participant.role)}
                              <span className="text-xs text-muted-foreground capitalize">
                                {participant.role}
                              </span>
                              {participant.is_muted && (
                                <span className="text-xs text-red-500 ml-1">🔇 Muted</span>
                              )}
                            </div>
                          </div>
                        </div>
                         {participant.profile_id !== profileId && (isHost || isCoHost) && (
                           <div className="flex gap-1 flex-wrap">
                             <Button
                               size="sm"
                               variant="ghost"
                               onClick={() => toggleParticipantMute(participant.profile_id, participant.is_muted)}
                               className="h-7 px-2 text-xs"
                             >
                               {participant.is_muted ? 'Unmute' : 'Mute'}
                             </Button>

                             {/* Host-only controls */}
                             {isHost && (
                               <>
                                 {(participant.role === 'listener' || participant.role === 'speaker') && (
                                   <Button
                                     size="sm"
                                     variant="outline"
                                     onClick={() => promoteToCoHost(participant.profile_id)}
                                     className="h-7 px-2 text-xs bg-blue-600 text-white hover:bg-blue-700"
                                   >
                                     👑 Make Co-Host
                                   </Button>
                                 )}

                                 {participant.role === 'co_host' && (
                                   <Button
                                     size="sm"
                                     variant="outline"
                                     onClick={() => demoteCoHost(participant.profile_id)}
                                     className="h-7 px-2 text-xs bg-red-600 text-white hover:bg-red-700"
                                   >
                                     Remove Co-Host
                                   </Button>
                                 )}
                               </>
                             )}
                           </div>
                         )}
                      </div>
                    ))}
                  </div>
                </div>
              </ScrollArea>
            </div>
          )}

          {/* Chat Panel */}
          {showChat && (
            <div className="w-80 border-l flex flex-col">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">Chat</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowChat(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <ScrollArea className="flex-1 p-4" ref={chatScrollRef}>
                <div className="space-y-3">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="flex flex-col gap-1">
                      <div className="flex items-center gap-2">
                        <Avatar className="w-6 h-6">
                          <AvatarFallback className="text-xs">
                            {message.profile_id.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs font-medium">
                          {message.profile_id === profileId ? 'You' : message.profile_id.slice(0, 8)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {new Date(message.created_at || '').toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm ml-8">{message.message}</p>
                    </div>
                  ))}
                </div>
              </ScrollArea>

              <form onSubmit={sendMessage} className="p-4 border-t">
                <div className="flex gap-2">
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type a message..."
                    className="flex-1"
                  />
                  <Button type="submit" size="sm" disabled={!newMessage.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </form>
            </div>
          )}
        </div>

        {/* Tip Modal */}
        {showTipModal && tipTarget && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-80">
              <h3 className="font-semibold mb-4">Send a Tip</h3>
              <div className="flex gap-2 mb-4">
                {[1, 5, 10, 25].map((amount) => (
                  <Button
                    key={amount}
                    variant="outline"
                    onClick={() => sendTip(amount)}
                    className="flex-1"
                  >
                    ${amount}
                  </Button>
                ))}
              </div>
              <Button
                variant="ghost"
                onClick={() => setShowTipModal(false)}
                className="w-full"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
