import React, { useState, useEffect, useRef } from 'react';
import { VoiceStory, storyService } from '@/services/storyService';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Play, Pause, SkipBack, SkipForward, 
  Heart, MessageCircle, Share2, X, 
  Eye, BarChart3, Clock
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';

interface StoryViewerProps {
  stories: VoiceStory[];
  initialIndex?: number;
  onClose: () => void;
  currentUserId?: string;
}

export const StoryViewer: React.FC<StoryViewerProps> = ({
  stories,
  initialIndex = 0,
  onClose,
  currentUserId
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showPoll, setShowPoll] = useState(false);
  const [selectedPollOption, setSelectedPollOption] = useState<number | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentStory = stories[currentIndex];

  useEffect(() => {
    if (currentStory && currentUserId) {
      // Mark story as viewed
      storyService.viewStory(currentStory.id, currentUserId);
    }
  }, [currentStory, currentUserId]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      if (audio.duration) {
        setProgress(0);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(100);
      // Auto-advance to next story
      setTimeout(() => {
        nextStory();
      }, 1000);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentStory]);

  useEffect(() => {
    if (isPlaying) {
      progressIntervalRef.current = setInterval(updateProgress, 100);
    } else {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [isPlaying]);

  const updateProgress = () => {
    const audio = audioRef.current;
    if (audio && audio.duration) {
      const progressPercent = (audio.currentTime / audio.duration) * 100;
      setProgress(progressPercent);
    }
  };

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const nextStory = () => {
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setProgress(0);
      setIsPlaying(false);
      setSelectedPollOption(null);
    } else {
      onClose();
    }
  };

  const previousStory = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setProgress(0);
      setIsPlaying(false);
      setSelectedPollOption(null);
    }
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: currentStory.title || 'Voice Story',
        text: currentStory.transcript || 'Check out this voice story',
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentStory) return null;

  const backgroundStyle = currentStory.background_image_url
    ? { backgroundImage: `url(${currentStory.background_image_url})` }
    : { backgroundColor: currentStory.background_color };

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Background */}
      <div 
        className="absolute inset-0 bg-cover bg-center blur-sm"
        style={backgroundStyle}
      />
      <div className="absolute inset-0 bg-black/40" />

      {/* Story Content */}
      <div className="relative w-full max-w-md mx-auto h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 text-white">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium">
                {currentStory.profile_id.slice(0, 2).toUpperCase()}
              </span>
            </div>
            <div>
              <p className="font-medium">{currentStory.profile_id}</p>
              <p className="text-xs text-white/80">
                {formatDistanceToNow(new Date(currentStory.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="px-4 mb-4">
          <Progress value={progress} className="h-1 bg-white/30" />
        </div>

        {/* Main Content Area */}
        <div 
          className="flex-1 flex flex-col justify-center items-center text-center p-6 bg-cover bg-center"
          style={backgroundStyle}
        >
          <div className="bg-black/40 rounded-lg p-6 backdrop-blur-sm max-w-sm">
            {/* Title */}
            {currentStory.title && (
              <h2 className="text-xl font-bold text-white mb-4">
                {currentStory.title}
              </h2>
            )}

            {/* Audio Player */}
            <audio
              ref={audioRef}
              src={currentStory.audio_url}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            />

            {/* Play/Pause Button */}
            <Button
              size="lg"
              onClick={togglePlayPause}
              className="mb-4 bg-white/20 hover:bg-white/30 text-white border-white/30"
            >
              {isPlaying ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6" />
              )}
            </Button>

            {/* Duration */}
            <p className="text-white/80 text-sm mb-4">
              <Clock className="h-4 w-4 inline mr-1" />
              {formatDuration(currentStory.audio_duration)}
            </p>

            {/* Transcript */}
            {currentStory.transcript && (
              <p className="text-white/90 text-sm mb-4 leading-relaxed">
                {currentStory.transcript}
              </p>
            )}

            {/* Poll */}
            {currentStory.story_type === 'voice_poll' && currentStory.poll_question && (
              <div className="mb-4">
                <h3 className="text-white font-medium mb-3">
                  {currentStory.poll_question}
                </h3>
                <div className="space-y-2">
                  {currentStory.poll_options?.map((option: string, index: number) => (
                    <Button
                      key={index}
                      variant={selectedPollOption === index ? "default" : "outline"}
                      className="w-full text-sm"
                      onClick={() => setSelectedPollOption(index)}
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Tags */}
            {currentStory.tags && currentStory.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-4">
                {currentStory.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    #{tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center justify-center gap-4 text-white/80 text-sm">
              <span className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                {currentStory.view_count}
              </span>
              {currentStory.reply_count > 0 && (
                <span className="flex items-center gap-1">
                  <MessageCircle className="h-4 w-4" />
                  {currentStory.reply_count}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between p-4">
          {/* Navigation */}
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={previousStory}
              disabled={currentIndex === 0}
              className="text-white hover:bg-white/20"
            >
              <SkipBack className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextStory}
              disabled={currentIndex === stories.length - 1}
              className="text-white hover:bg-white/20"
            >
              <SkipForward className="h-5 w-5" />
            </Button>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
            >
              <Heart className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20"
            >
              <MessageCircle className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white hover:bg-white/20"
            >
              <Share2 className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* Story Indicator */}
        <div className="flex justify-center gap-1 pb-4">
          {stories.map((_, index) => (
            <div
              key={index}
              className={`h-1 w-8 rounded-full ${
                index === currentIndex ? 'bg-white' : 'bg-white/30'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};