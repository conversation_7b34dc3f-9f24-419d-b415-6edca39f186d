import React, { useState, useEffect, useRef } from 'react';
import { VoiceStory, storyService } from '@/services/storyService';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Play, Pause, SkipBack, SkipForward,
  Heart, MessageCircle, Share2, X,
  Eye, BarChart3, Clock, Volume2, VolumeX
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface StoryViewerProps {
  stories: VoiceStory[];
  initialIndex?: number;
  onClose: () => void;
  currentUserId?: string;
}

export const StoryViewer: React.FC<StoryViewerProps> = ({
  stories,
  initialIndex = 0,
  onClose,
  currentUserId
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showPoll, setShowPoll] = useState(false);
  const [selectedPollOption, setSelectedPollOption] = useState<number | null>(null);
  const [usernames, setUsernames] = useState<{[key: string]: string}>({});
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentStory = stories[currentIndex];

  // Fetch usernames for all story creators
  useEffect(() => {
    const fetchUsernames = async () => {
      const uniqueProfileIds = [...new Set(stories.map(story => story.profile_id))];
      const usernameMap: {[key: string]: string} = {};

      for (const profileId of uniqueProfileIds) {
        try {
          // Try to get username from profiles table
          const { data: profile } = await supabase
            .from('profiles')
            .select('username, wallet_address')
            .eq('wallet_address', profileId)
            .single();

          if (profile?.username) {
            usernameMap[profileId] = profile.username;
          } else {
            // Fallback to shortened wallet address
            usernameMap[profileId] = profileId.startsWith('0x')
              ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
              : profileId;
          }
        } catch (error) {
          // Fallback to shortened wallet address
          usernameMap[profileId] = profileId.startsWith('0x')
            ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
            : profileId;
        }
      }

      setUsernames(usernameMap);
    };

    fetchUsernames();
  }, [stories]);

  useEffect(() => {
    if (currentStory && currentUserId) {
      // Mark story as viewed
      storyService.viewStory(currentStory.id, currentUserId);
    }
  }, [currentStory, currentUserId]);

  // Helper functions
  const isVideoStory = (story: VoiceStory) => {
    return story.background_image_url && (
      story.background_image_url.includes('.mp4') ||
      story.background_image_url.includes('.mov') ||
      story.background_image_url.includes('.webm') ||
      (story as any).media_type === 'video'
    );
  };

  const hasAudio = (story: VoiceStory) => {
    return story.audio_url &&
           story.audio_url !== 'no-audio' &&
           story.audio_url !== 'video-audio' &&
           story.audio_url !== 'placeholder';
  };

  const getDisplayName = (profileId: string) => {
    return usernames[profileId] || 'Loading...';
  };

  // Navigation functions
  const goToNext = () => {
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setProgress(0);
      setIsPlaying(false);
    } else {
      onClose();
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setProgress(0);
      setIsPlaying(false);
    }
  };

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      if (audio.duration) {
        setProgress(0);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(100);
      // Auto-advance to next story
      setTimeout(() => {
        goToNext();
      }, 1000);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentStory]);

  useEffect(() => {
    if (isPlaying) {
      progressIntervalRef.current = setInterval(updateProgress, 100);
    } else {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [isPlaying]);

  const updateProgress = () => {
    const audio = audioRef.current;
    if (audio && audio.duration) {
      const progressPercent = (audio.currentTime / audio.duration) * 100;
      setProgress(progressPercent);
    }
  };

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  // Use the goToNext and goToPrevious functions defined earlier

  const handleShare = async () => {
    try {
      await navigator.share({
        title: currentStory.title || 'Voice Story',
        text: currentStory.transcript || 'Check out this voice story',
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentStory) return null;

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Story Content */}
      <div className="relative w-full max-w-md mx-auto h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 text-white bg-gradient-to-b from-black/60 to-transparent">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10 border-2 border-white/30">
              <AvatarFallback className="bg-white/20 text-white">
                {getDisplayName(currentStory.profile_id).slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">{getDisplayName(currentStory.profile_id)}</p>
              <p className="text-xs text-white/80">
                {formatDistanceToNow(new Date(currentStory.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="px-4 mb-2">
          <Progress value={progress} className="h-1 bg-white/30" />
        </div>

        {/* Main Media Content */}
        <div className="flex-1 relative overflow-hidden">
          {isVideoStory(currentStory) ? (
            /* Video Story */
            <div className="w-full h-full relative">
              <video
                ref={videoRef}
                src={currentStory.background_image_url}
                className="w-full h-full object-cover"
                autoPlay
                loop
                muted={isVideoMuted}
                playsInline
              />

              {/* Video Controls */}
              <div className="absolute bottom-20 right-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsVideoMuted(!isVideoMuted)}
                  className="text-white bg-black/40 hover:bg-black/60 rounded-full w-10 h-10 p-0"
                >
                  {isVideoMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          ) : currentStory.background_image_url ? (
            /* Image Story */
            <div className="w-full h-full relative">
              <img
                src={currentStory.background_image_url}
                alt="Story"
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            /* Voice-only Story */
            <div
              className="w-full h-full flex items-center justify-center"
              style={{ backgroundColor: currentStory.background_color }}
            >
              <div className="text-center text-white p-8">
                <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Volume2 className="h-12 w-12" />
                </div>
                {currentStory.title && (
                  <h2 className="text-2xl font-bold mb-4">{currentStory.title}</h2>
                )}
                <p className="text-white/80">Voice Story</p>
              </div>
            </div>
          )}

          {/* Audio Player (hidden) */}
          {hasAudio(currentStory) && (
            <audio
              ref={audioRef}
              src={currentStory.audio_url}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            />
          )}

          {/* Play/Pause Button for Audio (only show if audio exists and not a video) */}
          {hasAudio(currentStory) && !isVideoStory(currentStory) && (
            <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
                size="lg"
                onClick={togglePlayPause}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 rounded-full w-16 h-16"
              >
                {isPlaying ? (
                  <Pause className="h-6 w-6" />
                ) : (
                  <Play className="h-6 w-6" />
                )}
              </Button>
            </div>
          )}

          {/* Story Title Overlay (for image/video stories) */}
          {(currentStory.background_image_url || isVideoStory(currentStory)) && currentStory.title && (
            <div className="absolute bottom-32 left-0 right-0 px-4">
              <div className="bg-black/60 rounded-lg p-4 backdrop-blur-sm">
                <h2 className="text-xl font-bold text-white text-center">
                  {currentStory.title}
                </h2>
              </div>
            </div>
          )}

          {/* Tags Overlay */}
          {currentStory.tags && currentStory.tags.length > 0 && (currentStory.background_image_url || isVideoStory(currentStory)) && (
            <div className="absolute top-20 left-4 right-4">
              <div className="flex flex-wrap gap-1">
                {currentStory.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs bg-black/60 text-white border-white/30">
                    #{tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Navigation Areas (tap to go next/previous) */}
          <div className="absolute inset-0 flex">
            {/* Previous Story Area */}
            <div
              className="w-1/3 h-full cursor-pointer"
              onClick={goToPrevious}
            />

            {/* Next Story Area */}
            <div
              className="w-2/3 h-full cursor-pointer"
              onClick={goToNext}
            />
          </div>
        </div>

        {/* Bottom Actions */}
        <div className="p-4 bg-gradient-to-t from-black/60 to-transparent">
          {/* Stats */}
          <div className="flex items-center justify-between text-white/80 text-sm mb-4">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                {currentStory.view_count}
              </span>
              {currentStory.reply_count > 0 && (
                <span className="flex items-center gap-1">
                  <MessageCircle className="h-4 w-4" />
                  {currentStory.reply_count}
                </span>
              )}
            </div>

            {/* Story Counter */}
            <div className="text-white/60 text-xs">
              {currentIndex + 1} of {stories.length}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center gap-6">
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
            >
              <Heart className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
            >
              <MessageCircle className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
            >
              <Share2 className="h-5 w-5" />
            </Button>
          </div>

          {/* Story Progress Indicators */}
          <div className="flex justify-center gap-1 mt-4">
            {stories.map((_, index) => (
              <div
                key={index}
                className={`h-1 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-white w-8'
                    : index < currentIndex
                      ? 'bg-white/60 w-6'
                      : 'bg-white/30 w-6'
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};