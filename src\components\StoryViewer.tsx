import React, { useState, useEffect, useRef } from 'react';
import { VoiceStory, storyService } from '@/services/storyService';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Play, Pause, SkipBack, SkipForward,
  Heart, MessageCircle, Share2, X,
  Eye, BarChart3, Clock, Volume2, VolumeX, Trash2, Send
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

interface StoryViewerProps {
  stories: VoiceStory[];
  initialIndex?: number;
  onClose: () => void;
  currentUserId?: string;
}

export const StoryViewer: React.FC<StoryViewerProps> = ({
  stories,
  initialIndex = 0,
  onClose,
  currentUserId
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showPoll, setShowPoll] = useState(false);
  const [selectedPollOption, setSelectedPollOption] = useState<number | null>(null);
  const [usernames, setUsernames] = useState<{[key: string]: string}>({});
  const [isVideoMuted, setIsVideoMuted] = useState(false);
  const [isLiked, setIsLiked] = useState(false);
  const [likeCount, setLikeCount] = useState(0);
  const [showComments, setShowComments] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const currentStory = stories[currentIndex];

  // Fetch usernames for all story creators
  useEffect(() => {
    const fetchUsernames = async () => {
      const uniqueProfileIds = [...new Set(stories.map(story => story.profile_id))];
      const usernameMap: {[key: string]: string} = {};

      for (const profileId of uniqueProfileIds) {
        try {
          // Try to get username from profiles table
          const { data: profile } = await supabase
            .from('profiles')
            .select('username, wallet_address')
            .eq('wallet_address', profileId)
            .single();

          if (profile?.username) {
            usernameMap[profileId] = profile.username;
          } else {
            // Fallback to shortened wallet address
            usernameMap[profileId] = profileId.startsWith('0x')
              ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
              : profileId;
          }
        } catch (error) {
          // Fallback to shortened wallet address
          usernameMap[profileId] = profileId.startsWith('0x')
            ? `${profileId.slice(0, 6)}...${profileId.slice(-4)}`
            : profileId;
        }
      }

      setUsernames(usernameMap);
    };

    fetchUsernames();
  }, [stories]);

  useEffect(() => {
    if (currentStory && currentUserId) {
      // Mark story as viewed
      storyService.viewStory(currentStory.id, currentUserId);
    }
  }, [currentStory, currentUserId]);

  // Helper functions
  const isVideoStory = (story: VoiceStory) => {
    return story.background_image_url && (
      story.background_image_url.includes('.mp4') ||
      story.background_image_url.includes('.mov') ||
      story.background_image_url.includes('.webm') ||
      (story as any).media_type === 'video'
    );
  };

  const hasAudio = (story: VoiceStory) => {
    return story.audio_url &&
           story.audio_url !== 'no-audio' &&
           story.audio_url !== 'video-audio' &&
           story.audio_url !== 'placeholder';
  };

  const getDisplayName = (profileId: string) => {
    return usernames[profileId] || 'Loading...';
  };

  // Navigation functions
  const goToNext = () => {
    if (currentIndex < stories.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setProgress(0);
      setIsPlaying(false);
    } else {
      onClose();
    }
  };

  const goToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setProgress(0);
      setIsPlaying(false);
    }
  };

  // Story interaction handlers
  const handleLike = async () => {
    if (!currentUserId) return;

    try {
      // Toggle like status
      setIsLiked(!isLiked);
      setLikeCount(prev => isLiked ? prev - 1 : prev + 1);

      // TODO: Implement actual like API call
      // await storyService.likeStory(currentStory.id, currentUserId, !isLiked);

      toast.success(isLiked ? 'Removed like' : 'Story liked!');
    } catch (error) {
      console.error('Error liking story:', error);
      // Revert optimistic update
      setIsLiked(isLiked);
      setLikeCount(prev => isLiked ? prev + 1 : prev - 1);
      toast.error('Failed to like story');
    }
  };

  const handleComment = () => {
    setShowComments(true);
  };

  const handleDeleteStory = async () => {
    if (!currentUserId || currentStory.profile_id !== currentUserId) return;

    try {
      await storyService.deleteStory(currentStory.id);
      toast.success('Story deleted successfully');

      // Remove story from list and close viewer if no more stories
      const updatedStories = stories.filter((_, index) => index !== currentIndex);
      if (updatedStories.length === 0) {
        onClose();
      } else {
        // Adjust current index if needed
        const newIndex = currentIndex >= updatedStories.length ? updatedStories.length - 1 : currentIndex;
        setCurrentIndex(newIndex);
      }
    } catch (error) {
      console.error('Error deleting story:', error);
      toast.error('Failed to delete story');
    }
    setShowDeleteConfirm(false);
  };

  const isOwnStory = currentUserId && currentStory.profile_id === currentUserId;

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      if (audio.duration) {
        setProgress(0);
      }
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setProgress(100);
      // Auto-advance to next story
      setTimeout(() => {
        goToNext();
      }, 1000);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [currentStory]);

  useEffect(() => {
    if (isPlaying) {
      progressIntervalRef.current = setInterval(updateProgress, 100);
    } else {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    }

    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, [isPlaying]);

  const updateProgress = () => {
    const audio = audioRef.current;
    if (audio && audio.duration) {
      const progressPercent = (audio.currentTime / audio.duration) * 100;
      setProgress(progressPercent);
    }
  };

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  // Use the goToNext and goToPrevious functions defined earlier

  const handleShare = async () => {
    try {
      await navigator.share({
        title: currentStory.title || 'Voice Story',
        text: currentStory.transcript || 'Check out this voice story',
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!currentStory) return null;

  return (
    <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
      {/* Story Content */}
      <div className="relative w-full max-w-md mx-auto h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 text-white bg-gradient-to-b from-black/60 to-transparent">
          <div className="flex items-center gap-3">
            <Avatar className="w-10 h-10 border-2 border-white/30">
              <AvatarFallback className="bg-white/20 text-white">
                {getDisplayName(currentStory.profile_id).slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium">{getDisplayName(currentStory.profile_id)}</p>
              <p className="text-xs text-white/80">
                {formatDistanceToNow(new Date(currentStory.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/20"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="px-4 mb-2">
          <Progress value={progress} className="h-1 bg-white/30" />
        </div>

        {/* Main Media Content */}
        <div className="flex-1 relative overflow-hidden">
          {isVideoStory(currentStory) ? (
            /* Video Story */
            <div className="w-full h-full relative">
              <video
                ref={videoRef}
                src={currentStory.background_image_url}
                className="w-full h-full object-cover"
                autoPlay
                loop
                muted={isVideoMuted}
                playsInline
              />

              {/* Video Controls */}
              <div className="absolute bottom-20 right-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsVideoMuted(!isVideoMuted)}
                  className="text-white bg-black/40 hover:bg-black/60 rounded-full w-10 h-10 p-0"
                >
                  {isVideoMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          ) : currentStory.background_image_url ? (
            /* Image Story */
            <div className="w-full h-full relative">
              <img
                src={currentStory.background_image_url}
                alt="Story"
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            /* Voice-only Story */
            <div
              className="w-full h-full flex items-center justify-center"
              style={{ backgroundColor: currentStory.background_color }}
            >
              <div className="text-center text-white p-8">
                <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Volume2 className="h-12 w-12" />
                </div>
                {currentStory.title && (
                  <h2 className="text-2xl font-bold mb-4">{currentStory.title}</h2>
                )}
                <p className="text-white/80">Voice Story</p>
              </div>
            </div>
          )}

          {/* Audio Player (hidden) */}
          {hasAudio(currentStory) && (
            <audio
              ref={audioRef}
              src={currentStory.audio_url}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            />
          )}

          {/* Play/Pause Button for Audio (only show if audio exists and not a video) */}
          {hasAudio(currentStory) && !isVideoStory(currentStory) && (
            <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
              <Button
                size="lg"
                onClick={togglePlayPause}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30 rounded-full w-16 h-16"
              >
                {isPlaying ? (
                  <Pause className="h-6 w-6" />
                ) : (
                  <Play className="h-6 w-6" />
                )}
              </Button>
            </div>
          )}

          {/* Story Title Overlay (for image/video stories) */}
          {(currentStory.background_image_url || isVideoStory(currentStory)) && currentStory.title && (
            <div className="absolute bottom-32 left-0 right-0 px-4">
              <div className="bg-black/60 rounded-lg p-4 backdrop-blur-sm">
                <h2 className="text-xl font-bold text-white text-center">
                  {currentStory.title}
                </h2>
              </div>
            </div>
          )}

          {/* Tags Overlay */}
          {currentStory.tags && currentStory.tags.length > 0 && (currentStory.background_image_url || isVideoStory(currentStory)) && (
            <div className="absolute top-20 left-4 right-4">
              <div className="flex flex-wrap gap-1">
                {currentStory.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs bg-black/60 text-white border-white/30">
                    #{tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Navigation Areas (tap to go next/previous) */}
          <div className="absolute inset-0 flex">
            {/* Previous Story Area */}
            <div
              className="w-1/3 h-full cursor-pointer"
              onClick={goToPrevious}
            />

            {/* Next Story Area */}
            <div
              className="w-2/3 h-full cursor-pointer"
              onClick={goToNext}
            />
          </div>
        </div>

        {/* Bottom Actions */}
        <div className="p-4 bg-gradient-to-t from-black/60 to-transparent">
          {/* Stats */}
          <div className="flex items-center justify-between text-white/80 text-sm mb-4">
            <div className="flex items-center gap-4">
              <span className="flex items-center gap-1">
                <Eye className="h-4 w-4" />
                {currentStory.view_count}
              </span>
              {currentStory.reply_count > 0 && (
                <span className="flex items-center gap-1">
                  <MessageCircle className="h-4 w-4" />
                  {currentStory.reply_count}
                </span>
              )}
            </div>

            {/* Story Counter */}
            <div className="text-white/60 text-xs">
              {currentIndex + 1} of {stories.length}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-center gap-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLike}
              className={`rounded-full w-12 h-12 p-0 ${
                isLiked
                  ? 'text-red-500 hover:bg-red-500/20'
                  : 'text-white hover:bg-white/20'
              }`}
            >
              <Heart className={`h-5 w-5 ${isLiked ? 'fill-current' : ''}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleComment}
              className="text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
            >
              <MessageCircle className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white hover:bg-white/20 rounded-full w-12 h-12 p-0"
            >
              <Share2 className="h-5 w-5" />
            </Button>
            {isOwnStory && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDeleteConfirm(true)}
                className="text-red-400 hover:bg-red-500/20 rounded-full w-12 h-12 p-0"
              >
                <Trash2 className="h-5 w-5" />
              </Button>
            )}
          </div>

          {/* Story Progress Indicators */}
          <div className="flex justify-center gap-1 mt-4">
            {stories.map((_, index) => (
              <div
                key={index}
                className={`h-1 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-white w-8'
                    : index < currentIndex
                      ? 'bg-white/60 w-6'
                      : 'bg-white/30 w-6'
                }`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="absolute inset-0 bg-black/80 flex items-center justify-center z-10">
          <div className="bg-white rounded-lg p-6 max-w-sm mx-4">
            <h3 className="text-lg font-semibold mb-4">Delete Story?</h3>
            <p className="text-gray-600 mb-6">This action cannot be undone.</p>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={handleDeleteStory}
                className="flex-1"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Comments Modal */}
      {showComments && (
        <div className="absolute inset-0 bg-black/80 flex items-end z-10">
          <div className="bg-white rounded-t-lg w-full max-h-[70vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-semibold">Comments</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowComments(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="flex-1 overflow-y-auto p-4">
              <div className="text-center text-gray-500 py-8">
                <MessageCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>No comments yet</p>
                <p className="text-sm">Be the first to comment!</p>
              </div>
            </div>

            <div className="p-4 border-t">
              <div className="flex gap-2">
                <input
                  type="text"
                  placeholder="Add a comment..."
                  className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
                <Button size="sm">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};