import React, { useState } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Drawer, DrawerContent } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { X, DollarSign, Loader2, Coins } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/sonner';
import { useProfiles } from '@/contexts/SimpleProfileContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { useWallet } from '@/contexts/WalletContext';
import walletService from '@/services/walletService';

interface TipModalWithoutWalletProps {
  isOpen: boolean;
  onClose: () => void;
  recipientAddress: string;
  messageId?: string; // Optional message ID for notifications
}

const TipModalWithoutWallet: React.FC<TipModalWithoutWalletProps> = ({
  isOpen,
  onClose,
  recipientAddress,
  messageId
}) => {
  const isMobile = useIsMobile();
  const [amount, setAmount] = useState('0.01');
  const [message, setMessage] = useState('');
  const [selectedChain, setSelectedChain] = useState<'ETH' | 'SOL'>('ETH');
  const [isProcessing, setIsProcessing] = useState(false);
  const { getProfileByAddress } = useProfiles();
  const { addNotification } = useNotifications();
  const { wallet, sendTip, sendSolanaTip, isLoading, balance, solanaBalance } = useWallet();

  // Get recipient profile
  const recipientProfile = getProfileByAddress(recipientAddress);
  const recipientName = recipientProfile?.displayName || walletService.formatAddress(recipientAddress);

  // Predefined tip amounts based on chain
  const tipAmounts = selectedChain === 'ETH' 
    ? ['0.001', '0.01', '0.05', '0.1']
    : ['0.1', '1', '5', '10']; // Higher amounts for SOL since it's cheaper

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Only allow valid numbers
    const value = e.target.value;
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
    }
  };

  const handleSendTip = async () => {
    if (!wallet) {
      toast.error('Please connect your wallet first');
      return;
    }

    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    try {
      setIsProcessing(true);
      
      const transaction = selectedChain === 'ETH' 
        ? await sendTip(recipientAddress, amount, message)
        : await sendSolanaTip(recipientAddress, amount, message);
      
      if (transaction) {
        toast.success(`Tip of ${amount} ${selectedChain} sent successfully!`);
        
        // Add notification for the recipient
        if (messageId) {
          addNotification(
            'tip',
            wallet.address,
            recipientAddress,
            messageId,
            {
              tipAmount: amount,
              currency: selectedChain,
              message: message || undefined
            }
          );
        }
        
        onClose();
      } else {
        toast.error('Failed to send tip');
      }
    } catch (error) {
      console.error('Error sending tip:', error);
      toast.error('Failed to send tip');
    } finally {
      setIsProcessing(false);
    }
  };

  const ModalContent = () => (
    <div className="flex flex-col h-full max-h-[calc(90vh-2rem)]">
      {/* Header - Fixed */}
      <div className="flex justify-between items-center p-4 border-b">
        <h2 className="text-xl font-semibold">Send a tip</h2>
        <Button variant="ghost" size="icon" onClick={onClose} disabled={isProcessing}>
          <X size={20} />
        </Button>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <div>
          <Label htmlFor="recipient">Recipient</Label>
          <Input
            id="recipient"
            value={recipientName}
            disabled
          />
          <div className="mt-2 p-2 bg-muted rounded-md">
            <Label className="text-xs font-medium">Wallet Address:</Label>
            <p className="text-xs font-mono break-all text-muted-foreground mt-1">
              {recipientAddress}
            </p>
          </div>
        </div>

        <div>
          <Label htmlFor="chain">Blockchain Network</Label>
          <Select value={selectedChain} onValueChange={(value: 'ETH' | 'SOL') => setSelectedChain(value)}>
            <SelectTrigger className="w-full">
              <SelectValue>
                <div className="flex items-center gap-2">
                  <Coins className="h-4 w-4" />
                  {selectedChain === 'ETH' ? 'Ethereum (ETH)' : 'Solana (SOL)'}
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="z-50" position="popper" sideOffset={4}>
              <SelectItem value="ETH">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                  Ethereum (ETH)
                </div>
              </SelectItem>
              <SelectItem value="SOL">
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 rounded-full bg-purple-500"></div>
                  Solana (SOL)
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="amount">Amount ({selectedChain})</Label>
          <div className="relative">
            <Input
              id="amount"
              value={amount}
              onChange={handleAmountChange}
              className="pl-8"
              disabled={isProcessing}
              type="number"
              step="0.001"
              min="0.001"
            />
            <DollarSign className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>

          {/* Quick amount buttons */}
          <div className="flex flex-wrap gap-2 mt-2">
            {tipAmounts.map((amt) => (
              <Button
                key={amt}
                variant="outline"
                size="sm"
                onClick={() => setAmount(amt)}
                className={amount === amt ? 'bg-voicechain-purple text-white' : ''}
                disabled={isProcessing}
              >
                {amt} {selectedChain}
              </Button>
            ))}
          </div>

          {/* Balance display */}
          <p className="text-xs text-muted-foreground mt-1">
            Your balance: {selectedChain === 'ETH' ? (wallet?.balance || '0') : (solanaBalance || '0')} {selectedChain}
          </p>
        </div>

        <div>
          <Label htmlFor="message">Message (optional)</Label>
          <Textarea
            id="message"
            placeholder="Add a message with your tip..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="resize-none"
            rows={3}
            disabled={isProcessing}
          />
        </div>
      </div>

      {/* Footer - Fixed */}
      <div className="p-4 border-t">
        <Button
          onClick={handleSendTip}
          className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
          disabled={isProcessing || !wallet || parseFloat(amount) <= 0}
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <DollarSign className="mr-2 h-4 w-4" />
              Send Tip
            </>
          )}
        </Button>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="max-h-[85vh]">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-hidden">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default TipModalWithoutWallet;
