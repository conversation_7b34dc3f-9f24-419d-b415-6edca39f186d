import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Header, DialogFooter } from '@/components/ui/dialog';
import { Drawer, DrawerContent, DrawerHeader, DrawerFooter } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { X, Upload, Twitter, Globe, Github } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { toast } from '@/components/ui/sonner';
import { UserProfile, UserProfileUpdate } from '@/types/user-profile';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { supabase } from '@/integrations/supabase/client';
import { ScrollArea } from '@/components/ui/scroll-area';
import { forceSyncProfileToSupabase } from '@/utils/profileSyncUtils';

interface SimpleProfileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  profile: UserProfile;
  onSave: (update: UserProfileUpdate) => void;
}

const SimpleProfileEditModal: React.FC<SimpleProfileEditModalProps> = ({
  isOpen,
  onClose,
  profile,
  onSave
}) => {
  const isMobile = useIsMobile();
  const [formData, setFormData] = useState<UserProfileUpdate & {
    profileImageFile?: File;
    coverImageFile?: File;
  }>({
    username: profile.username,
    displayName: profile.displayName,
    bio: profile.bio || '',
    profileImageUrl: profile.profileImageUrl || '',
    coverImageUrl: profile.coverImageUrl || '',
    socialLinks: {
      twitter: profile.socialLinks?.twitter || '',
      github: profile.socialLinks?.github || '',
      website: profile.socialLinks?.website || ''
    }
  });
  
  const [isUploading, setIsUploading] = useState(false);
  
  // Refs for file inputs
  const profileImageInputRef = React.useRef<HTMLInputElement>(null);
  const coverImageInputRef = React.useRef<HTMLInputElement>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    if (name.startsWith('social.')) {
      const socialType = name.split('.')[1];
      setFormData(prev => ({
        ...prev,
        socialLinks: {
          ...prev.socialLinks || {},
          [socialType]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            profileImageUrl: event.target?.result as string,
            profileImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setFormData(prev => ({
            ...prev,
            coverImageUrl: event.target?.result as string,
            coverImageFile: file
          }));
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImageToSupabase = async (file: File, path: string): Promise<string> => {
    try {
      // Upload image to storage
      const { data, error } = await supabase.storage
        .from('profiles')
        .upload(`${profile.address}/${path}`, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) {
        throw new Error(error.message);
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profiles')
        .getPublicUrl(data.path);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  const handleSubmit = async () => {
    try {
      setIsUploading(true);
      const loadingToast = toast.loading('Updating profile...');

      // Validate the form data
      if (!formData.displayName || formData.displayName.trim() === '') {
        toast.dismiss(loadingToast);
        toast.error('Display name is required');
        setIsUploading(false);
        return;
      }

      if (!formData.username || formData.username.trim() === '') {
        toast.dismiss(loadingToast);
        toast.error('Username is required');
        setIsUploading(false);
        return;
      }

      // Create a deep copy of the form data to avoid reference issues
      const update: UserProfileUpdate = {
        displayName: formData.displayName.trim(),
        username: formData.username.trim(),
        bio: formData.bio?.trim() || '',
        socialLinks: { ...formData.socialLinks },
        profileImageUrl: formData.profileImageUrl,
        coverImageUrl: formData.coverImageUrl
      };

      // Upload profile image if changed
      if (formData.profileImageFile) {
        try {
          const profileImageUrl = await uploadImageToSupabase(formData.profileImageFile, `profile-${Date.now()}`);
          update.profileImageUrl = profileImageUrl;
        } catch (error) {
          console.error('Error uploading profile image:', error);
          toast.dismiss(loadingToast);
          toast.error('Failed to upload profile image');
          setIsUploading(false);
          return;
        }
      }

      // Upload cover image if changed
      if (formData.coverImageFile) {
        try {
          const coverImageUrl = await uploadImageToSupabase(formData.coverImageFile, `cover-${Date.now()}`);
          update.coverImageUrl = coverImageUrl;
        } catch (error) {
          console.error('Error uploading cover image:', error);
          toast.dismiss(loadingToast);
          toast.error('Failed to upload cover image');
          setIsUploading(false);
          return;
        }
      }

      console.log("SimpleProfileEditModal - Updating profile with:", update);

      try {
        // Call the onSave function with the update data
        await onSave(update);
        
        // Force sync with Supabase to ensure data is persisted
        const updatedProfile = {
          ...profile,
          ...update,
          socialLinks: {
            ...profile.socialLinks,
            ...update.socialLinks
          }
        };
        
        await forceSyncProfileToSupabase(updatedProfile);
        console.log("Force synced profile to Supabase:", updatedProfile);
        
        // Dismiss loading toast
        toast.dismiss(loadingToast);
        toast.success('Profile updated successfully!');
        
        // Close the modal
        onClose();
      } catch (error) {
        console.error("Error updating profile:", error);
        toast.dismiss(loadingToast);
        toast.error("Failed to update profile");
      }
    } catch (error) {
      console.error("Error in profile update flow:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsUploading(false);
    }
  };

  // Function to handle removing profile image
  const removeProfileImage = () => {
    setFormData(prev => ({
      ...prev,
      profileImageUrl: '',
      profileImageFile: undefined
    }));
  };

  // Function to handle removing cover image
  const removeCoverImage = () => {
    setFormData(prev => ({
      ...prev,
      coverImageUrl: '',
      coverImageFile: undefined
    }));
  };

  const ModalContent = () => (
    <Dialog 
      open={isOpen} 
      onOpenChange={(open) => {
        console.log('Dialog onOpenChange triggered, new open state:', open);
        if (!open) onClose();
      }}
    >
      <DialogContent className="sm:max-w-md p-6">
        <DialogHeader className="p-0">
          <div className="flex justify-between items-center w-full mb-4">
            <DialogTitle className="text-xl font-semibold">Edit Profile</DialogTitle>
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => {
                console.log('Close button clicked');
                onClose();
              }} 
              type="button"
            >
              <X size={20} />
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <div>
            <Label htmlFor="displayName">Display Name</Label>
            <Input
              id="displayName"
              name="displayName"
              value={formData.displayName}
              onChange={handleInputChange}
              placeholder="Your display name"
            />
          </div>

          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="username"
            />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              name="bio"
              value={formData.bio}
              onChange={handleInputChange}
              placeholder="Tell us about yourself"
              className="resize-none h-24"
            />
          </div>

          <div>
            <Label htmlFor="social.twitter">Twitter</Label>
            <Input
              id="social.twitter"
              name="social.twitter"
              value={formData.socialLinks?.twitter || ''}
              onChange={handleInputChange}
              placeholder="@username"
            />
          </div>

          <div>
            <Label htmlFor="social.github">GitHub</Label>
            <Input
              id="social.github"
              name="social.github"
              value={formData.socialLinks?.github || ''}
              onChange={handleInputChange}
              placeholder="username"
            />
          </div>

          <div>
            <Label htmlFor="social.website">Website</Label>
            <Input
              id="social.website"
              name="social.website"
              value={formData.socialLinks?.website || ''}
              onChange={handleInputChange}
              placeholder="https://yourwebsite.com"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="profileImage">Profile Image</Label>
              <Input
                id="profileImage"
                name="profileImage"
                type="file"
                ref={profileImageInputRef}
                onChange={handleProfileImageChange}
              />
            </div>
            {formData.profileImageUrl && (
              <div className="flex items-center">
                <Avatar>
                  <AvatarImage src={formData.profileImageUrl} />
                  <AvatarFallback>
                    <Upload size={20} />
                  </AvatarFallback>
                </Avatar>
                <Button
                  onClick={removeProfileImage}
                  className="ml-2"
                >
                  Remove
                </Button>
              </div>
            )}
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="coverImage">Cover Image</Label>
              <Input
                id="coverImage"
                name="coverImage"
                type="file"
                ref={coverImageInputRef}
                onChange={handleCoverImageChange}
              />
            </div>
            {formData.coverImageUrl && (
              <div className="flex items-center">
                <Avatar>
                  <AvatarImage src={formData.coverImageUrl} />
                  <AvatarFallback>
                    <Globe size={20} />
                  </AvatarFallback>
                </Avatar>
                <Button
                  onClick={removeCoverImage}
                  className="ml-2"
                >
                  Remove
                </Button>
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button
            onClick={handleSubmit}
            className="w-full bg-voicechain-purple hover:bg-voicechain-accent"
            disabled={isUploading}
            type="button"
          >
            {isUploading ? 'Saving...' : 'Save Profile'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={open => !open && onClose()}>
        <DrawerContent className="px-4 py-6">
          <ModalContent />
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-md p-6">
        <ModalContent />
      </DialogContent>
    </Dialog>
  );
};

export default SimpleProfileEditModal;
