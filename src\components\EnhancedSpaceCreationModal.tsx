import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Calendar, Clock, Users, Mic, MessageSquare, Video, Radio, Globe, Lock, Crown, Shield, Zap, Music, Gamepad2, BookOpen, Briefcase, Heart, DollarSign, Eye, Filter } from 'lucide-react';
import { spaceService } from '@/services/spaceService';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface SpaceCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSpaceCreated: () => void;
  channelId: string;
  profileId: string;
}

export const EnhancedSpaceCreationModal: React.FC<SpaceCreationModalProps> = ({
  isOpen,
  onClose,
  onSpaceCreated,
  channelId,
  profileId,
}) => {
  // Basic settings
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('general');
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  
  // Scheduling
  const [isStartingNow, setIsStartingNow] = useState(true);
  const [scheduledStart, setScheduledStart] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState('60');
  
  // Capacity and permissions
  const [maxParticipants, setMaxParticipants] = useState(1000);
  const [spaceType, setSpaceType] = useState<'public' | 'private' | 'invite-only'>('public');
  const [requireApproval, setRequireApproval] = useState(false);
  
  // Features
  const [chatEnabled, setChatEnabled] = useState(true);
  const [audienceCanRequestMic, setAudienceCanRequestMic] = useState(true);
  const [isRecorded, setIsRecorded] = useState(false);
  const [allowScreenShare, setAllowScreenShare] = useState(false);
  const [enableReactions, setEnableReactions] = useState(true);
  const [enableTipping, setEnableTipping] = useState(false);
  
  // Moderation
  const [autoModeration, setAutoModeration] = useState(true);
  const [profanityFilter, setProfanityFilter] = useState(true);
  const [requireVerification, setRequireVerification] = useState(false);
  
  const [isCreating, setIsCreating] = useState(false);

  const { toast } = useToast();

  const spaceCategories = [
    { value: 'general', label: 'General Discussion', icon: MessageSquare },
    { value: 'music', label: 'Music & Audio', icon: Music },
    { value: 'tech', label: 'Technology', icon: Zap },
    { value: 'business', label: 'Business & Finance', icon: Briefcase },
    { value: 'education', label: 'Education & Learning', icon: BookOpen },
    { value: 'gaming', label: 'Gaming', icon: Gamepad2 },
    { value: 'wellness', label: 'Health & Wellness', icon: Heart },
    { value: 'entertainment', label: 'Entertainment', icon: Video },
  ];

  const addTag = () => {
    if (currentTag.trim() && !tags.includes(currentTag.trim()) && tags.length < 5) {
      setTags([...tags, currentTag.trim()]);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setCategory('general');
    setTags([]);
    setCurrentTag('');
    setIsStartingNow(true);
    setScheduledStart('');
    setEstimatedDuration('60');
    setMaxParticipants(1000);
    setSpaceType('public');
    setRequireApproval(false);
    setChatEnabled(true);
    setAudienceCanRequestMic(true);
    setIsRecorded(false);
    setAllowScreenShare(false);
    setEnableReactions(true);
    setEnableTipping(false);
    setAutoModeration(true);
    setProfanityFilter(true);
    setRequireVerification(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      toast({
        title: "Title required",
        description: "Please enter a title for your space.",
        variant: "destructive",
      });
      return;
    }

    if (!isStartingNow && !scheduledStart) {
      toast({
        title: "Schedule required",
        description: "Please set a start time for your scheduled space.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      // Use a default channel ID if none provided
      const defaultChannelId = '9c51a054-32dd-4856-a6fa-1cdf5aa51be2'; // First available channel

      // Look up the profile UUID from the wallet address
      let hostProfileId = profileId;

      // If profileId looks like a wallet address (starts with 0x), look up the UUID
      if (profileId && profileId.startsWith('0x')) {
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .eq('wallet_address', profileId)
          .single();

        if (profileError) {
          console.error('Error finding profile:', profileError);
          toast({
            title: "Profile not found",
            description: "Please make sure you have a profile set up.",
            variant: "destructive",
          });
          return;
        }

        hostProfileId = profile.id;
      }

      const spaceData = {
        title: title.trim(),
        description: description.trim() || undefined,
        channel_id: channelId || defaultChannelId,
        host_profile_id: hostProfileId,
        scheduled_start: isStartingNow ? undefined : scheduledStart,
        max_participants: maxParticipants,
        chat_enabled: chatEnabled,
        audience_can_request_mic: audienceCanRequestMic,
        is_recorded: isRecorded,
        settings: {
          category,
          tags,
          spaceType,
          requireApproval,
          allowScreenShare,
          enableReactions,
          enableTipping,
          autoModeration,
          profanityFilter,
          requireVerification,
          estimatedDuration: parseInt(estimatedDuration),
        }
      };

      const space = await spaceService.createLiveStream(spaceData);

      if (space) {
        toast({
          title: "🎉 Advanced Space Created!",
          description: isStartingNow ? "Your space is now live with all features enabled." : "Your space has been scheduled with advanced settings.",
        });
        
        onSpaceCreated();
        onClose();
        resetForm();
      } else {
        throw new Error('Failed to create space');
      }
    } catch (error) {
      console.error('Error creating space:', error);
      toast({
        title: "Error creating space",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  const minDateTime = new Date();
  minDateTime.setMinutes(minDateTime.getMinutes() + 5);
  const minDateTimeString = minDateTime.toISOString().slice(0, 16);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Radio className="h-5 w-5 text-primary animate-pulse" />
            Create Advanced Live Space
            <Badge variant="secondary" className="ml-2">Pro Features</Badge>
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic" className="flex items-center gap-2">
              <Radio className="h-4 w-4" />
              Basic Info
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Features
            </TabsTrigger>
            <TabsTrigger value="moderation" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Moderation
            </TabsTrigger>
          </TabsList>

          <form onSubmit={handleSubmit} className="space-y-6 mt-6">
            <TabsContent value="basic" className="space-y-6">
              {/* Title */}
              <div className="space-y-2">
                <Label htmlFor="title" className="flex items-center gap-2">
                  <Crown className="h-4 w-4" />
                  Title *
                </Label>
                <Input
                  id="title"
                  placeholder="What's your space about?"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  maxLength={100}
                  required
                  className="text-lg"
                />
                <p className="text-xs text-muted-foreground">
                  {title.length}/100 characters
                </p>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Tell people what to expect in your space..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  maxLength={500}
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  {description.length}/500 characters
                </p>
              </div>

              {/* Category */}
              <div className="space-y-2">
                <Label>Category</Label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {spaceCategories.map((cat) => {
                      const IconComponent = cat.icon;
                      return (
                        <SelectItem key={cat.value} value={cat.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" />
                            {cat.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags (Max 5)</Label>
                <div className="flex gap-2 flex-wrap mb-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground" onClick={() => removeTag(tag)}>
                      #{tag} ×
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={currentTag}
                    onChange={(e) => setCurrentTag(e.target.value)}
                    placeholder="Add a tag..."
                    maxLength={20}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" onClick={addTag} variant="outline" size="sm" disabled={tags.length >= 5}>
                    Add
                  </Button>
                </div>
              </div>

              {/* Space Type */}
              <div className="space-y-2">
                <Label>Space Type</Label>
                <Select value={spaceType} onValueChange={(value: 'public' | 'private' | 'invite-only') => setSpaceType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4" />
                        Public - Anyone can join
                      </div>
                    </SelectItem>
                    <SelectItem value="private">
                      <div className="flex items-center gap-2">
                        <Lock className="h-4 w-4" />
                        Private - Invite only
                      </div>
                    </SelectItem>
                    <SelectItem value="invite-only">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        Invite Only - Host approval required
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Scheduling */}
              <div className="space-y-3">
                <Label>Start Time</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="start-now"
                      checked={isStartingNow}
                      onCheckedChange={setIsStartingNow}
                    />
                    <Label htmlFor="start-now" className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      Start immediately
                    </Label>
                  </div>

                  {!isStartingNow && (
                    <div className="space-y-2">
                      <Label htmlFor="scheduled-start">Scheduled Start Time</Label>
                      <Input
                        id="scheduled-start"
                        type="datetime-local"
                        value={scheduledStart}
                        onChange={(e) => setScheduledStart(e.target.value)}
                        min={minDateTimeString}
                        required={!isStartingNow}
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Estimated Duration */}
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Estimated Duration
                </Label>
                <Select value={estimatedDuration} onValueChange={setEstimatedDuration}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="90">1.5 hours</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                    <SelectItem value="180">3 hours</SelectItem>
                    <SelectItem value="240">4 hours</SelectItem>
                    <SelectItem value="480">8 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="space-y-6">
              {/* Capacity */}
              <div className="space-y-2">
                <Label htmlFor="max-participants" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Maximum Participants
                </Label>
                <Select value={maxParticipants.toString()} onValueChange={(value) => setMaxParticipants(parseInt(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10 participants</SelectItem>
                    <SelectItem value="25">25 participants</SelectItem>
                    <SelectItem value="50">50 participants</SelectItem>
                    <SelectItem value="100">100 participants</SelectItem>
                    <SelectItem value="250">250 participants</SelectItem>
                    <SelectItem value="500">500 participants</SelectItem>
                    <SelectItem value="1000">1000 participants</SelectItem>
                    <SelectItem value="5000">5000 participants</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              {/* Core Features */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Core Features
                </h4>

                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="chat-enabled" className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4" />
                      Enable Chat
                    </Label>
                    <Switch
                      id="chat-enabled"
                      checked={chatEnabled}
                      onCheckedChange={setChatEnabled}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="audience-mic" className="flex items-center gap-2">
                      <Mic className="h-4 w-4" />
                      Audience Can Request Mic
                    </Label>
                    <Switch
                      id="audience-mic"
                      checked={audienceCanRequestMic}
                      onCheckedChange={setAudienceCanRequestMic}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="record-space" className="flex items-center gap-2">
                      <Video className="h-4 w-4" />
                      Record Space
                    </Label>
                    <Switch
                      id="record-space"
                      checked={isRecorded}
                      onCheckedChange={setIsRecorded}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="screen-share" className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Allow Screen Share
                    </Label>
                    <Switch
                      id="screen-share"
                      checked={allowScreenShare}
                      onCheckedChange={setAllowScreenShare}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Engagement Features */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Engagement Features
                </h4>

                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="reactions" className="flex items-center gap-2">
                      <Heart className="h-4 w-4" />
                      Enable Reactions
                    </Label>
                    <Switch
                      id="reactions"
                      checked={enableReactions}
                      onCheckedChange={setEnableReactions}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="tipping" className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Enable Tipping
                    </Label>
                    <Switch
                      id="tipping"
                      checked={enableTipping}
                      onCheckedChange={setEnableTipping}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="approval" className="flex items-center gap-2">
                      <Crown className="h-4 w-4" />
                      Require Host Approval
                    </Label>
                    <Switch
                      id="approval"
                      checked={requireApproval}
                      onCheckedChange={setRequireApproval}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="moderation" className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Moderation & Safety
                </h4>

                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-mod" className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Auto Moderation
                    </Label>
                    <Switch
                      id="auto-mod"
                      checked={autoModeration}
                      onCheckedChange={setAutoModeration}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="profanity" className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      Profanity Filter
                    </Label>
                    <Switch
                      id="profanity"
                      checked={profanityFilter}
                      onCheckedChange={setProfanityFilter}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="verification" className="flex items-center gap-2">
                      <Crown className="h-4 w-4" />
                      Require Verification
                    </Label>
                    <Switch
                      id="verification"
                      checked={requireVerification}
                      onCheckedChange={setRequireVerification}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Actions */}
            <div className="flex gap-3 pt-6 border-t">
              <Button type="button" variant="outline" onClick={handleClose} className="flex-1">
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isCreating}
                className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {isCreating ? 'Creating...' : `🚀 Create ${isStartingNow ? 'Live' : 'Scheduled'} Space`}
              </Button>
            </div>
          </form>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
