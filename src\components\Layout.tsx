
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Header from '@/components/Header';
import BottomNav from '@/components/BottomNav';
import RecordingModal from '@/components/RecordingModal';
import { EnhancedSpaceCreationModal } from '@/components/EnhancedSpaceCreationModal';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { LogIn, LogOut, MessageCircle, Plus, Mic, Radio } from 'lucide-react';

import { MediaFile } from './MediaUploader';

interface LayoutProps {
  children: React.ReactNode;
  connectedAccount: string;
  onWalletConnect: (account: string) => void;
  onRecordingComplete?: (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => void;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  connectedAccount,
  onWalletConnect,
  onRecordingComplete
}) => {
  const [isRecordingModalOpen, setIsRecordingModalOpen] = useState(false);
  const [isSpaceCreationModalOpen, setIsSpaceCreationModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('home');
  const location = useLocation();
  const navigate = useNavigate();
  const { isAuthenticated, logout } = useAuth();

  // Set active tab based on route
  useEffect(() => {
    if (location.pathname === '/') {
      setActiveTab('home');
    // CHANNELS REMOVED
    } else if (location.pathname === '/notifications') {
      setActiveTab('notifications');
    } else if (location.pathname === '/profile') {
      setActiveTab('profile');
    } else if (location.pathname.startsWith('/chain-voice')) {
      setActiveTab('chain-voice');
    } else if (location.pathname === '/journals') {
      setActiveTab('journals');
    } else if (location.pathname === '/analytics') {
      setActiveTab('analytics');
    } else if (location.pathname === '/wallet') {
      setActiveTab('wallet');
    }
  }, [location.pathname]);

  // Handle recording icon click
  const handleRecordClick = () => {
    if (connectedAccount) {
      setIsRecordingModalOpen(true);
    }
  };

  // Handle space creation click
  const handleSpaceCreationClick = () => {
    if (connectedAccount) {
      setIsSpaceCreationModalOpen(true);
    }
  };

  // Handle space creation completion
  const handleSpaceCreated = () => {
    setIsSpaceCreationModalOpen(false);
    // Optionally navigate to spaces tab or refresh
  };

  // Handle recording completion
  const handleRecordingComplete = (audioBlob: Blob, transcript: string, duration?: number, media?: MediaFile[]) => {
    if (onRecordingComplete) {
      // Pass all parameters including duration and media files
      onRecordingComplete(audioBlob, transcript, duration, media);
      setIsRecordingModalOpen(false);
    }
  };

  // Handle login button click
  const handleLoginClick = () => {
    navigate('/login');
  };

  // Handle logout button click
  const handleLogoutClick = () => {
    logout();
    navigate('/login');
  };

  return (
    <div className="min-h-screen flex flex-col bg-voicechain-dark text-foreground">
      <Header onWalletConnect={onWalletConnect} connectedAccount={connectedAccount} />

      {!isAuthenticated && !location.pathname.includes('/login') && !location.pathname.includes('/signup') && !location.pathname.includes('/forgot-password') && (
        <div className="container mx-auto px-4 py-4 flex justify-end">
          <Button
            onClick={handleLoginClick}
            className="bg-voicechain-purple hover:bg-voicechain-accent"
          >
            <LogIn className="h-4 w-4 mr-2" />
            Login
          </Button>
        </div>
      )}

      {/* Logout button moved to header */}

      <main className="container mx-auto flex-1 px-4 py-6 pb-safe-bottom max-w-3xl">
        {children}
      </main>

      {isAuthenticated && connectedAccount && (
        <>
          {/* Floating Create Button with Dropdown */}
          <div className="fixed bottom-safe-offset right-4 z-30">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  className="bg-voicechain-purple hover:bg-voicechain-accent rounded-full w-14 h-14 flex items-center justify-center shadow-lg"
                >
                  <Plus size={24} className="text-white" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem onClick={handleRecordClick} className="cursor-pointer">
                  <Mic className="mr-2 h-4 w-4" />
                  Create Voice Post
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleSpaceCreationClick} className="cursor-pointer">
                  <Radio className="mr-2 h-4 w-4" />
                  Create Live Space
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <BottomNav
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />

          <RecordingModal
            isOpen={isRecordingModalOpen}
            onClose={() => setIsRecordingModalOpen(false)}
            onRecordingComplete={handleRecordingComplete}
            userAddress={connectedAccount}
          />

          <EnhancedSpaceCreationModal
            isOpen={isSpaceCreationModalOpen}
            onClose={() => setIsSpaceCreationModalOpen(false)}
            onSpaceCreated={handleSpaceCreated}
            channelId="9c51a054-32dd-4856-a6fa-1cdf5aa51be2"
            profileId={connectedAccount}
          />
        </>
      )}
    </div>
  );
};

export default Layout;
