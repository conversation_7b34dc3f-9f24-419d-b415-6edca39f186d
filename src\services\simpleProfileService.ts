/**
 * Simple Profile Service
 * A lightweight profile service that uses Supabase for persistence
 */

import { UserProfile, UserProfileUpdate, createDefaultProfile } from '@/types/user-profile';
import { toast } from '@/components/ui/sonner';
import { supabase } from '@/integrations/supabase/client';

// In-memory cache of profiles (only for the current session)
const profileCache: Record<string, UserProfile> = {};

/**
 * Get a profile by address
 * @param address The wallet address
 * @returns The profile or null if not found
 */
export async function getProfileByAddress(address: string): Promise<UserProfile | null> {
  if (!address) return null;

  const normalizedAddress = address.toLowerCase();

  // Check cache first (but only for this session)
  if (profileCache[normalizedAddress]) {
    console.log(`Using cached profile for ${normalizedAddress}`);
    return profileCache[normalizedAddress];
  }

  // Try to get from Supabase using the new RPC function
  try {
    console.log(`Getting profile from Supabase for ${normalizedAddress}`);

    // Try to use the get_or_create_profile RPC function first
    try {
      const { data: rpcResult, error: rpcError } = await supabase.rpc('get_or_create_profile', {
        wallet_addr: normalizedAddress
      });

      if (!rpcError && rpcResult && rpcResult.length > 0) {
        const data = rpcResult[0];
        console.log(`Found/created profile via RPC for ${normalizedAddress}`, data);

        // Convert the Supabase profile to our UserProfile format
        const profile: UserProfile = {
          address: normalizedAddress,
          walletAddress: data.wallet_address || normalizedAddress,
          username: data.username || `user_${normalizedAddress.substring(2, 8).toLowerCase()}`,
          displayName: data.display_name || `User ${normalizedAddress.substring(0, 6)}`,
          bio: data.bio || '',
          profileImageUrl: data.avatar_url || '',
          coverImageUrl: data.cover_image_url || '',
          socialLinks: data.social_links && typeof data.social_links === 'object' ? {
            twitter: (data.social_links as any).twitter || undefined,
            github: (data.social_links as any).github || undefined,
            website: (data.social_links as any).website || undefined
          } : {},
          stats: {
            posts: data.post_count || 0,
            likes: data.like_count || 0,
            tips: data.tip_count || 0
          },
          joinedDate: new Date(data.created_at),
          verification: {
            isVerified: !!data.verification_type,
            type: data.verification_type ?
              (data.verification_type === 'owner' ? 'owner' :
                data.verification_type === 'creator' ? 'creator' :
                  data.verification_type === 'developer' ? 'developer' : undefined) : undefined,
            since: data.created_at ? new Date(data.created_at) : undefined
          }
        };

        // Cache the profile in memory
        profileCache[normalizedAddress] = profile;
        return profile;
      } else {
        throw rpcError || new Error('RPC function failed');
      }
    } catch (rpcError) {
      console.error('RPC function failed, falling back to direct query:', rpcError);

      // Fallback to direct query
      // First check if profile exists by direct UUID match (when address is actually a UUID)
      let supabaseQuery = supabase.from('profiles').select('*');

      // Try to parse as UUID
      let isUuid = false;
      try {
        // This is just a check, we're not using the result
        const uuid = normalizedAddress;
        if (uuid.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
          isUuid = true;
        }
      } catch (e) {
        // Not a UUID, that's fine
        isUuid = false;
      }

      // Different query depending on if it's a UUID or wallet address
      let { data, error } = isUuid
        ? await supabaseQuery.eq('id', normalizedAddress).maybeSingle()
        : await supabaseQuery.ilike('wallet_address', normalizedAddress).maybeSingle();

      if (error) {
        console.warn('Error getting profile from Supabase:', error);
        return null;
      } else if (data) {
        console.log(`Found profile in Supabase for ${normalizedAddress}`, data);

        // Convert the Supabase profile to our UserProfile format
        const profile: UserProfile = {
          address: normalizedAddress,
          walletAddress: data.wallet_address || normalizedAddress,
          username: data.username || `user_${normalizedAddress.substring(2, 8).toLowerCase()}`,
          displayName: data.display_name || `User ${normalizedAddress.substring(0, 6)}`,
          bio: data.bio || '',
          profileImageUrl: data.avatar_url || '',
          coverImageUrl: data.cover_image_url || '',
          socialLinks: data.social_links && typeof data.social_links === 'object' ? {
            twitter: (data.social_links as any).twitter || undefined,
            github: (data.social_links as any).github || undefined,
            website: (data.social_links as any).website || undefined
          } : {},
          stats: {
            posts: data.post_count || 0,
            likes: data.like_count || 0,
            tips: data.tip_count || 0
          },
          joinedDate: new Date(data.created_at),
          verification: {
            isVerified: !!data.verification_type,
            type: data.verification_type ?
              (data.verification_type === 'owner' ? 'owner' :
                data.verification_type === 'creator' ? 'creator' :
                  data.verification_type === 'developer' ? 'developer' : undefined) : undefined,
            since: data.created_at ? new Date(data.created_at) : undefined
          }
        };

        // Cache the profile in memory
        profileCache[normalizedAddress] = profile;
        return profile;
      }
    }
  } catch (supabaseError) {
    console.error('Error connecting to Supabase:', supabaseError);
    return null;
  }

  // Create a default profile if not found
  console.log(`Creating default profile for ${normalizedAddress}`);
  const defaultProfile = createDefaultProfile(normalizedAddress);

  // Cache the profile
  profileCache[normalizedAddress] = defaultProfile;

  return defaultProfile;
}

/**
 * Create a new profile
 * @param profile The profile to create
 * @returns The created profile
 */
export async function createProfile(address: string, profileData: UserProfileUpdate): Promise<UserProfile> {
  if (!address) {
    throw new Error('Profile address is required');
  }

  const normalizedAddress = address.toLowerCase();

  // Use the provided data or create a default profile
  const profile: UserProfile = {
    ...createDefaultProfile(normalizedAddress),
    ...profileData,
    address: normalizedAddress,
    walletAddress: normalizedAddress
  };

  // Save to Supabase
  try {
    console.log(`Creating profile in Supabase for ${normalizedAddress}`);

    // Generate a UUID for the profile ID if it's not already a UUID
    let profileId;
    
    try {
      if (normalizedAddress.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        // It's already a UUID
        profileId = normalizedAddress;
      } else {
        // Generate a new UUID
        profileId = crypto.randomUUID();
      }
    } catch (e) {
      // Default to generating a UUID
      profileId = crypto.randomUUID();
    }

    // Try to use the RPC function first
    try {
      const { data: rpcResult, error: rpcError } = await supabase.rpc('update_profile_by_address', {
        p_wallet_address: normalizedAddress,
        p_username: profile.username,
        p_display_name: profile.displayName,
        p_bio: profile.bio || '',
        p_avatar_url: profile.profileImageUrl || '',
        p_cover_image_url: profile.coverImageUrl || '',
        p_social_links: profile.socialLinks || {}
      });

      if (!rpcError && rpcResult) {
        console.log('Profile created/updated in Supabase via RPC:', rpcResult);
        // Update the profile with the returned data
        const updatedProfile = {
          ...profile,
          address: normalizedAddress,
          walletAddress: rpcResult.wallet_address,
          username: rpcResult.username,
          displayName: rpcResult.display_name,
          bio: rpcResult.bio,
          profileImageUrl: rpcResult.avatar_url,
          coverImageUrl: rpcResult.cover_image_url,
          socialLinks: rpcResult.social_links || {}
        };
        profileCache[normalizedAddress] = updatedProfile;
        return updatedProfile;
      } else {
        throw rpcError || new Error('RPC function returned no data');
      }
    } catch (rpcError) {
      console.error('Error with RPC method, falling back to direct insert:', rpcError);
      
      // Fall back to direct insert
      // Insert new profile with explicit UUID
      const { error } = await supabase
        .from('profiles')
        .insert({
          id: profileId,
          wallet_address: normalizedAddress,
          username: profile.username,
          display_name: profile.displayName,
          bio: profile.bio || '',
          avatar_url: profile.profileImageUrl || '',
          cover_image_url: profile.coverImageUrl || '',
          social_links: profile.socialLinks || {},
          post_count: 0,
          like_count: 0,
          tip_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        
      if (error) {
        console.error('Error creating profile in Supabase:', error);
      } else {
        console.log(`Created new profile in Supabase for ${normalizedAddress}`);
      }
    }
  } catch (error) {
    console.error('Error creating profile in Supabase:', error);
  }

  // Cache the profile
  profileCache[normalizedAddress] = profile;

  return profile;
}

/**
 * Update a profile
 * @param address The wallet address
 * @param update The profile update
 * @returns The updated profile
 */
export async function updateProfile(address: string, update: UserProfileUpdate): Promise<UserProfile> {
  if (!address) {
    throw new Error('Address is required');
  }

  const normalizedAddress = address.toLowerCase();
  console.log(`🔄 PROFILE UPDATE: Starting update for ${normalizedAddress}`, update);

  // Get the current profile from database (not cache) to ensure we have latest data
  let currentProfile = await getProfileFromDatabase(normalizedAddress);
  
  if (!currentProfile) {
    // If no profile exists, create one first
    console.log('No existing profile found, creating new one');
    currentProfile = await createProfile(normalizedAddress, update);
    return currentProfile;
  }

  // Update the profile
  const updatedProfile: UserProfile = {
    ...currentProfile,
    ...update,
    socialLinks: {
      ...currentProfile.socialLinks,
      ...update.socialLinks
    },
    stats: {
      ...currentProfile.stats,
      ...update.stats
    }
  };

  // Save to database first
  const dbUpdateSuccess = await saveProfileToDatabase(normalizedAddress, updatedProfile);
  
  if (dbUpdateSuccess) {
    // Only update cache after successful database save
    profileCache[normalizedAddress] = updatedProfile;
    console.log('✅ PROFILE UPDATE: Profile saved to database and cache updated');
    return updatedProfile;
  } else {
    console.log('❌ PROFILE UPDATE: Database update failed, profile not updated');
    throw new Error('Failed to update profile in database');
  }
}

/**
 * Get profile directly from database (bypassing cache)
 */
async function getProfileFromDatabase(address: string): Promise<UserProfile | null> {
  try {
    const normalizedAddress = address.toLowerCase();
    
    // Try to get from database using RPC function first
    try {
      const { data: rpcResult, error: rpcError } = await supabase.rpc('get_or_create_profile', {
        wallet_addr: normalizedAddress
      });

      if (!rpcError && rpcResult && rpcResult.length > 0) {
        const data = rpcResult[0];
        return convertDbToProfile(data, normalizedAddress);
      }
    } catch (rpcError) {
      console.error('RPC function failed:', rpcError);
    }

    // Fallback to direct query
    const isUuid = normalizedAddress.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
    
    let { data, error } = isUuid
      ? await supabase.from('profiles').select('*').eq('id', normalizedAddress).maybeSingle()
      : await supabase.from('profiles').select('*').ilike('wallet_address', normalizedAddress).maybeSingle();

    if (error || !data) {
      return null;
    }

    return convertDbToProfile(data, normalizedAddress);
  } catch (error) {
    console.error('Error getting profile from database:', error);
    return null;
  }
}

/**
 * Save profile to database
 */
async function saveProfileToDatabase(address: string, profile: UserProfile): Promise<boolean> {
  try {
    const normalizedAddress = address.toLowerCase();
    
    // Convert profile to database format
    const updateData = {
      username: profile.username,
      display_name: profile.displayName,
      bio: profile.bio || '',
      avatar_url: profile.profileImageUrl || '',
      cover_image_url: profile.coverImageUrl || '',
      social_links: profile.socialLinks || {},
      post_count: profile.stats?.posts || 0,
      like_count: profile.stats?.likes || 0,
      tip_count: profile.stats?.tips || 0,
      updated_at: new Date().toISOString()
    };

    // Try to find and update existing profile
    const isUuid = normalizedAddress.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i);
    
    const { data: existingProfile, error: findError } = isUuid
      ? await supabase.from('profiles').select('id').eq('id', normalizedAddress).maybeSingle()
      : await supabase.from('profiles').select('id').eq('wallet_address', normalizedAddress).maybeSingle();
      
    if (findError) {
      console.error('Error finding profile:', findError);
      return false;
    }

    if (existingProfile && existingProfile.id) {
      // Update existing profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', existingProfile.id);
        
      if (updateError) {
        console.error('Error updating profile in database:', updateError);
        return false;
      }
      
      console.log(`Profile updated in database for ${normalizedAddress}`);
      return true;
    } else {
      // Profile doesn't exist, create it
      const profileId = crypto.randomUUID();
      const { error: insertError } = await supabase
        .from('profiles')
        .insert({
          id: profileId,
          wallet_address: normalizedAddress,
          ...updateData,
          created_at: new Date().toISOString()
        });
        
      if (insertError) {
        console.error('Error creating profile in database:', insertError);
        return false;
      }
      
      console.log(`Profile created in database for ${normalizedAddress}`);
      return true;
    }
  } catch (error) {
    console.error('Error saving profile to database:', error);
    return false;
  }
}

/**
 * Convert database row to UserProfile
 */
function convertDbToProfile(data: any, address: string): UserProfile {
  return {
    address: address,
    walletAddress: data.wallet_address || address,
    username: data.username || `user_${address.substring(2, 8).toLowerCase()}`,
    displayName: data.display_name || `User ${address.substring(0, 6)}`,
    bio: data.bio || '',
    profileImageUrl: data.avatar_url || '',
    coverImageUrl: data.cover_image_url || '',
    socialLinks: data.social_links && typeof data.social_links === 'object' ? {
      twitter: (data.social_links as any).twitter || undefined,
      github: (data.social_links as any).github || undefined,
      website: (data.social_links as any).website || undefined
    } : {},
    stats: {
      posts: data.post_count || 0,
      likes: data.like_count || 0,
      tips: data.tip_count || 0
    },
    joinedDate: new Date(data.created_at),
    verification: {
      isVerified: !!data.verification_type,
      type: data.verification_type ?
        (data.verification_type === 'owner' ? 'owner' :
          data.verification_type === 'creator' ? 'creator' :
            data.verification_type === 'developer' ? 'developer' : undefined) : undefined,
      since: data.created_at ? new Date(data.created_at) : undefined
    }
  };
}

export default {
  getProfileByAddress,
  createProfile,
  updateProfile
};