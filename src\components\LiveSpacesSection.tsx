import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Radio, Users, Clock, Mic, MessageSquare, 
  Play, Volume2, Eye, Zap 
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { spaceService } from '@/services/spaceService';
import { supabase } from '@/integrations/supabase/client';

interface LiveSpace {
  id: string;
  title: string;
  description?: string;
  host_profile_id: string;
  participant_count: number;
  max_participants: number;
  chat_enabled: boolean;
  audience_can_request_mic: boolean;
  is_recorded: boolean;
  created_at: string;
  status: string;
  settings?: any;
}

interface LiveSpacesSectionProps {
  currentUserId?: string;
  onJoinSpace?: (spaceId: string) => void;
}

export const LiveSpacesSection: React.FC<LiveSpacesSectionProps> = ({
  currentUserId,
  onJoinSpace
}) => {
  const [liveSpaces, setLiveSpaces] = useState<LiveSpace[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hostnames, setHostnames] = useState<{[key: string]: string}>({});

  useEffect(() => {
    loadLiveSpaces();
    
    // Refresh every 30 seconds to show new live spaces
    const interval = setInterval(loadLiveSpaces, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadLiveSpaces = async () => {
    try {
      setIsLoading(true);
      const spaces = await spaceService.getLiveStreams();
      
      // Filter only live/active spaces
      const activeSpaces = spaces.filter(space => 
        space.status === 'live' || space.status === 'active'
      );
      
      setLiveSpaces(activeSpaces);
      
      // Fetch hostnames
      if (activeSpaces.length > 0) {
        await fetchHostnames(activeSpaces);
      }
    } catch (error) {
      console.error('Error loading live spaces:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchHostnames = async (spaces: LiveSpace[]) => {
    const uniqueHostIds = [...new Set(spaces.map(space => space.host_profile_id))];
    const hostnameMap: {[key: string]: string} = {};
    
    for (const hostId of uniqueHostIds) {
      try {
        const { data: profile } = await supabase
          .from('profiles')
          .select('username, wallet_address')
          .eq('id', hostId)
          .single();
          
        if (profile?.username) {
          hostnameMap[hostId] = profile.username;
        } else {
          // Try by wallet address if UUID lookup fails
          const { data: profileByWallet } = await supabase
            .from('profiles')
            .select('username, wallet_address')
            .eq('wallet_address', hostId)
            .single();
            
          if (profileByWallet?.username) {
            hostnameMap[hostId] = profileByWallet.username;
          } else {
            hostnameMap[hostId] = hostId.startsWith('0x') 
              ? `${hostId.slice(0, 6)}...${hostId.slice(-4)}`
              : hostId;
          }
        }
      } catch (error) {
        hostnameMap[hostId] = hostId.startsWith('0x') 
          ? `${hostId.slice(0, 6)}...${hostId.slice(-4)}`
          : hostId;
      }
    }
    
    setHostnames(hostnameMap);
  };

  const getHostDisplayName = (hostId: string) => {
    return hostnames[hostId] || 'Loading...';
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'music': return '🎵';
      case 'tech': return '💻';
      case 'business': return '💼';
      case 'education': return '📚';
      case 'gaming': return '🎮';
      case 'wellness': return '💚';
      case 'entertainment': return '🎬';
      default: return '💬';
    }
  };

  const handleJoinSpace = (spaceId: string) => {
    if (onJoinSpace) {
      onJoinSpace(spaceId);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Radio className="h-5 w-5 text-red-500 animate-pulse" />
          <h2 className="text-lg font-semibold">Live Spaces</h2>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (liveSpaces.length === 0) {
    return (
      <div className="text-center py-8">
        <Radio className="h-12 w-12 mx-auto text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Live Spaces</h3>
        <p className="text-gray-500 mb-4">Be the first to start a live conversation!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Radio className="h-5 w-5 text-red-500 animate-pulse" />
          <h2 className="text-lg font-semibold">Live Spaces</h2>
          <Badge variant="secondary" className="bg-red-100 text-red-700">
            {liveSpaces.length} Live
          </Badge>
        </div>
      </div>

      <div className="grid gap-4">
        {liveSpaces.map((space) => (
          <Card key={space.id} className="hover:shadow-md transition-shadow cursor-pointer border-l-4 border-l-red-500">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-lg">
                      {getCategoryIcon(space.settings?.category || 'general')}
                    </span>
                    <Badge variant="destructive" className="animate-pulse">
                      <Radio className="h-3 w-3 mr-1" />
                      LIVE
                    </Badge>
                  </div>
                  <CardTitle className="text-lg leading-tight">{space.title}</CardTitle>
                  {space.description && (
                    <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                      {space.description}
                    </p>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="pt-0">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-gray-500">
                  <div className="flex items-center gap-1">
                    <Avatar className="w-6 h-6">
                      <AvatarFallback className="text-xs">
                        {getHostDisplayName(space.host_profile_id).slice(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span>{getHostDisplayName(space.host_profile_id)}</span>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Users className="h-4 w-4" />
                    <span>{space.participant_count || 0}</span>
                  </div>
                  
                  {space.chat_enabled && (
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4" />
                    </div>
                  )}
                  
                  {space.is_recorded && (
                    <div className="flex items-center gap-1">
                      <Eye className="h-4 w-4" />
                    </div>
                  )}
                </div>
                
                <Button 
                  onClick={() => handleJoinSpace(space.id)}
                  size="sm"
                  className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                >
                  <Volume2 className="h-4 w-4 mr-1" />
                  Join
                </Button>
              </div>
              
              {/* Tags */}
              {space.settings?.tags && space.settings.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {space.settings.tags.slice(0, 3).map((tag: string, index: number) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                  {space.settings.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{space.settings.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
