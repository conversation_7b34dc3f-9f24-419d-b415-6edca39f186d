
import React from 'react';
import { Home, Hash, MessageCircle, Bell, User, Settings, Volume2, FileText, BarChart, Wallet } from 'lucide-react';
import { Button } from './ui/button';
import { useNavigate } from 'react-router-dom';

interface BottomNavProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const BottomNav: React.FC<BottomNavProps> = ({
  activeTab,
  setActiveTab
}) => {
  const navigate = useNavigate();

  const tabs = [
    { id: 'home', icon: Home, path: '/' },
    // CHANNELS REMOVED - WAS TOO BROKEN
    { id: 'chain-voice', icon: Volume2, path: '/chain-voice' },
    { id: 'notifications', icon: Bell, path: '/notifications' },
    { id: 'journals', icon: FileText, path: '/journals' },
    { id: 'analytics', icon: BarChart, path: '/analytics' },
    { id: 'wallet', icon: Wallet, path: '/wallet' },
    { id: 'profile', icon: User, path: '/profile' },
    { id: 'settings', icon: Settings, path: '/settings' }
  ];

  const handleTabClick = (tab: typeof tabs[0]) => {
    setActiveTab(tab.id);
    navigate(tab.path);
  };

  return (
    <nav className="fixed bottom-0 left-0 right-0 z-20 bg-voicechain-dark/95 backdrop-blur-lg border-t border-border/50 py-2 pb-safe">
      <div className="container mx-auto max-w-3xl px-4">
        <div className="flex items-center justify-between">
          {/* All tabs in a single row with even spacing */}
          <div className="flex items-center justify-between w-full">
            {tabs.map((tab) => (
              <Button
                key={tab.id}
                variant="ghost"
                size="icon"
                className={`flex items-center py-2 px-0 h-auto relative ${activeTab === tab.id ? 'text-voicechain-purple' : 'text-muted-foreground'
                  }`}
                onClick={() => handleTabClick(tab)}
              >
                <tab.icon size={18} />
              </Button>
            ))}
          </div>
        </div>
      </div>
      {/* Safe area spacer for devices with home indicators */}
      <div className="h-safe-bottom bg-voicechain-dark/95"></div>
    </nav>
  );
};

export default BottomNav;
