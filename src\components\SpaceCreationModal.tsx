import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Calendar, Clock, Users, Mic, MessageSquare, Video } from 'lucide-react';
import { spaceService } from '@/services/spaceService';
import { useToast } from '@/hooks/use-toast';

interface SpaceCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSpaceCreated: () => void;
  channelId: string;
  profileId: string;
}

export const SpaceCreationModal: React.FC<SpaceCreationModalProps> = ({
  isOpen,
  onClose,
  onSpaceCreated,
  channelId,
  profileId,
}) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [scheduledStart, setScheduledStart] = useState('');
  const [maxParticipants, setMaxParticipants] = useState(1000);
  const [chatEnabled, setChatEnabled] = useState(true);
  const [audienceCanRequestMic, setAudienceCanRequestMic] = useState(true);
  const [isRecorded, setIsRecorded] = useState(false);
  const [isStartingNow, setIsStartingNow] = useState(true);
  const [isCreating, setIsCreating] = useState(false);

  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      toast({
        title: "Title required",
        description: "Please enter a title for your space.",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);

    try {
      const spaceData = {
        title: title.trim(),
        description: description.trim() || undefined,
        channel_id: channelId,
        host_profile_id: profileId,
        scheduled_start: isStartingNow ? undefined : scheduledStart,
        max_participants: maxParticipants,
        chat_enabled: chatEnabled,
        audience_can_request_mic: audienceCanRequestMic,
        is_recorded: isRecorded,
      };

      const space = await spaceService.createLiveStream(spaceData);

      if (space) {
        toast({
          title: "Space created!",
          description: isStartingNow ? "Your space is now live." : "Your space has been scheduled.",
        });
        
        onSpaceCreated();
        onClose();
        resetForm();
      } else {
        throw new Error('Failed to create space');
      }
    } catch (error) {
      console.error('Error creating space:', error);
      toast({
        title: "Error creating space",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setScheduledStart('');
    setMaxParticipants(1000);
    setChatEnabled(true);
    setAudienceCanRequestMic(true);
    setIsRecorded(false);
    setIsStartingNow(true);
  };

  const handleClose = () => {
    onClose();
    resetForm();
  };

  const minDateTime = new Date();
  minDateTime.setMinutes(minDateTime.getMinutes() + 5);
  const minDateTimeString = minDateTime.toISOString().slice(0, 16);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mic className="h-5 w-5 text-primary" />
            Create Voice Space
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Input
              id="title"
              placeholder="What's your space about?"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              maxLength={100}
              required
            />
            <p className="text-xs text-muted-foreground">
              {title.length}/100 characters
            </p>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Tell people what to expect..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={500}
              rows={3}
            />
            <p className="text-xs text-muted-foreground">
              {description.length}/500 characters
            </p>
          </div>

          {/* Start Time */}
          <div className="space-y-3">
            <Label>Start Time</Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="start-now"
                  checked={isStartingNow}
                  onCheckedChange={setIsStartingNow}
                />
                <Label htmlFor="start-now" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Start now
                </Label>
              </div>
              
              {!isStartingNow && (
                <div className="space-y-2">
                  <Label htmlFor="scheduled-start">Schedule for later</Label>
                  <Input
                    id="scheduled-start"
                    type="datetime-local"
                    value={scheduledStart}
                    onChange={(e) => setScheduledStart(e.target.value)}
                    min={minDateTimeString}
                    required={!isStartingNow}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Max Participants */}
          <div className="space-y-2">
            <Label htmlFor="max-participants" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Max Participants
            </Label>
            <Input
              id="max-participants"
              type="number"
              min={10}
              max={10000}
              value={maxParticipants}
              onChange={(e) => setMaxParticipants(Number(e.target.value))}
            />
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <Label>Space Settings</Label>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  <Label htmlFor="chat-enabled">Enable Chat</Label>
                </div>
                <Switch
                  id="chat-enabled"
                  checked={chatEnabled}
                  onCheckedChange={setChatEnabled}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mic className="h-4 w-4" />
                  <Label htmlFor="mic-requests">Allow Mic Requests</Label>
                </div>
                <Switch
                  id="mic-requests"
                  checked={audienceCanRequestMic}
                  onCheckedChange={setAudienceCanRequestMic}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Video className="h-4 w-4" />
                  <Label htmlFor="record">Record Space</Label>
                </div>
                <Switch
                  id="record"
                  checked={isRecorded}
                  onCheckedChange={setIsRecorded}
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating}>
              {isCreating 
                ? 'Creating...' 
                : isStartingNow 
                ? 'Go Live' 
                : 'Schedule Space'
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};