import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  Globe,
  Headphones,
  Volume2,
  VolumeX,
  Mic,
  MicOff,
  Users,
  Settings,
  Maximize,
  RotateCcw,
  Zap,
  Move3D,
  RotateCw,
  ZoomIn,
  ZoomOut
} from 'lucide-react';
import { VoiceChatMessage } from '@/services/voiceChatService';
import AudioPlayer from '@/components/AudioPlayer';

interface SpatialChatViewProps {
  messages: VoiceChatMessage[];
  participants: any[];
  onSendMessage: (message: string) => void;
  onSendVoice: () => void;
  currentUserId: string;
}

interface Vector3D {
  x: number;
  y: number;
  z: number;
}

interface SpatialPosition extends Vector3D {
  rotationX: number;
  rotationY: number;
  rotationZ: number;
  scale: number;
}

interface SpatialMessage {
  id: string;
  message: VoiceChatMessage;
  position: SpatialPosition;
  isActive: boolean;
  distance: number;
  volume: number;
}

interface Camera {
  position: Vector3D;
  rotation: Vector3D;
  fov: number;
  zoom: number;
}

const SpatialChatView: React.FC<SpatialChatViewProps> = ({
  messages,
  participants,
  onSendMessage,
  onSendVoice,
  currentUserId
}) => {
  const [spatialMessages, setSpatialMessages] = useState<SpatialMessage[]>([]);
  const [camera, setCamera] = useState<Camera>({
    position: { x: 0, y: 0, z: 500 },
    rotation: { x: 0, y: 0, z: 0 },
    fov: 75,
    zoom: 1
  });
  const [isImmersiveMode, setIsImmersiveMode] = useState(false);
  const [spatialAudioEnabled, setSpatialAudioEnabled] = useState(true);
  const [activeVoiceMessage, setActiveVoiceMessage] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();

  // Calculate distance between two 3D points
  const calculateDistance = useCallback((pos1: Vector3D, pos2: Vector3D): number => {
    const dx = pos1.x - pos2.x;
    const dy = pos1.y - pos2.y;
    const dz = pos1.z - pos2.z;
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }, []);

  // Calculate spatial audio volume based on distance
  const calculateSpatialVolume = useCallback((distance: number): number => {
    const maxDistance = 1000;
    const minVolume = 0.1;
    const maxVolume = 1.0;

    if (distance >= maxDistance) return minVolume;

    // Inverse square law for realistic audio falloff
    const normalizedDistance = distance / maxDistance;
    const volume = maxVolume * (1 - normalizedDistance * normalizedDistance);

    return Math.max(minVolume, Math.min(maxVolume, volume));
  }, []);

  // Generate real 3D spatial positions for messages
  useEffect(() => {
    const newSpatialMessages: SpatialMessage[] = messages.map((message, index) => {
      // Create a 3D sphere distribution
      const phi = Math.acos(1 - 2 * (index / messages.length)); // Polar angle
      const theta = Math.PI * (1 + Math.sqrt(5)) * index; // Azimuthal angle (golden ratio)

      const radius = 200 + (index % 4) * 100; // Varying distances from center

      // Convert spherical to Cartesian coordinates
      const x = radius * Math.sin(phi) * Math.cos(theta);
      const y = radius * Math.sin(phi) * Math.sin(theta);
      const z = radius * Math.cos(phi);

      const position: SpatialPosition = {
        x,
        y,
        z,
        rotationX: Math.random() * 360,
        rotationY: Math.random() * 360,
        rotationZ: Math.random() * 360,
        scale: 0.8 + Math.random() * 0.4 // Random scale between 0.8 and 1.2
      };

      const distance = calculateDistance(position, camera.position);
      const volume = calculateSpatialVolume(distance);

      return {
        id: message.id,
        message,
        position,
        isActive: false,
        distance,
        volume
      };
    });

    setSpatialMessages(newSpatialMessages);
  }, [messages, camera.position, calculateDistance, calculateSpatialVolume]);

  // Handle mouse controls for 3D camera movement
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: event.clientX, y: event.clientY });
  }, []);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaX = event.clientX - lastMousePos.x;
    const deltaY = event.clientY - lastMousePos.y;

    setCamera(prev => ({
      ...prev,
      rotation: {
        x: prev.rotation.x + deltaY * 0.5,
        y: prev.rotation.y + deltaX * 0.5,
        z: prev.rotation.z
      }
    }));

    setLastMousePos({ x: event.clientX, y: event.clientY });
  }, [isDragging, lastMousePos]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Handle wheel zoom
  const handleWheel = useCallback((event: React.WheelEvent) => {
    event.preventDefault();
    const zoomDelta = event.deltaY > 0 ? 0.9 : 1.1;

    setCamera(prev => ({
      ...prev,
      zoom: Math.max(0.1, Math.min(5, prev.zoom * zoomDelta))
    }));
  }, []);

  // Keyboard controls for 3D movement
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const moveSpeed = 20;

      switch (event.key.toLowerCase()) {
        case 'w':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, z: prev.position.z - moveSpeed }
          }));
          break;
        case 's':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, z: prev.position.z + moveSpeed }
          }));
          break;
        case 'a':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, x: prev.position.x - moveSpeed }
          }));
          break;
        case 'd':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, x: prev.position.x + moveSpeed }
          }));
          break;
        case 'q':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, y: prev.position.y - moveSpeed }
          }));
          break;
        case 'e':
          setCamera(prev => ({
            ...prev,
            position: { ...prev.position, y: prev.position.y + moveSpeed }
          }));
          break;
      }
    };

    if (isImmersiveMode) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [isImmersiveMode]);

  const activateMessage = useCallback((messageId: string) => {
    setSpatialMessages(prev =>
      prev.map(msg => ({
        ...msg,
        isActive: msg.id === messageId
      }))
    );
    setActiveVoiceMessage(messageId);
  }, []);

  // Calculate 3D transform for each message
  const calculateTransform = useCallback((position: SpatialPosition): string => {
    const perspective = 1000;
    const scale = position.scale * camera.zoom;

    return `
      perspective(${perspective}px)
      rotateX(${camera.rotation.x}deg)
      rotateY(${camera.rotation.y}deg)
      rotateZ(${camera.rotation.z}deg)
      translate3d(
        ${position.x - camera.position.x}px,
        ${position.y - camera.position.y}px,
        ${position.z - camera.position.z}px
      )
      rotateX(${position.rotationX}deg)
      rotateY(${position.rotationY}deg)
      rotateZ(${position.rotationZ}deg)
      scale3d(${scale}, ${scale}, ${scale})
    `;
  }, [camera]);

  return (
    <div className={`relative w-full h-full ${isImmersiveMode ? 'fixed inset-0 z-50 bg-black' : 'h-96'}`}>
      {/* Real 3D Spatial Environment */}
      <div
        ref={containerRef}
        className="relative w-full h-full bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 overflow-hidden select-none"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
        style={{
          perspective: `${1000 / camera.zoom}px`,
          transformStyle: 'preserve-3d',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
      >
        {/* 3D Grid Environment */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            transform: `
              rotateX(${camera.rotation.x}deg)
              rotateY(${camera.rotation.y}deg)
              rotateZ(${camera.rotation.z}deg)
              scale(${camera.zoom})
            `,
            transformStyle: 'preserve-3d'
          }}
        >
          {/* 3D Grid Lines */}
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={`grid-${i}`}
              className="absolute border border-cyan-400/20"
              style={{
                width: '2000px',
                height: '2000px',
                left: '50%',
                top: '50%',
                transform: `
                  translate(-50%, -50%)
                  rotateX(${i * 18}deg)
                  translateZ(${(i - 10) * 100}px)
                `,
                transformStyle: 'preserve-3d'
              }}
            />
          ))}
        </div>

        {/* 3D Floating Particles */}
        <div className="absolute inset-0">
          {Array.from({ length: 50 }).map((_, i) => {
            const x = (Math.random() - 0.5) * 2000;
            const y = (Math.random() - 0.5) * 2000;
            const z = (Math.random() - 0.5) * 1000;

            return (
              <div
                key={`particle-${i}`}
                className="absolute w-2 h-2 bg-cyan-400 rounded-full opacity-60"
                style={{
                  left: '50%',
                  top: '50%',
                  transform: `
                    translate(-50%, -50%)
                    rotateX(${camera.rotation.x}deg)
                    rotateY(${camera.rotation.y}deg)
                    translate3d(${x}px, ${y}px, ${z}px)
                    scale(${camera.zoom})
                  `,
                  transformStyle: 'preserve-3d',
                  animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
                  animationDelay: `${Math.random() * 5}s`
                }}
              />
            );
          })}
        </div>

        {/* Camera Position Indicator */}
        <div
          className="absolute z-20 pointer-events-none"
          style={{
            left: '50%',
            top: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <div className="relative">
            <Avatar className="w-16 h-16 ring-4 ring-cyan-400 ring-opacity-60 shadow-lg shadow-cyan-500/50">
              <AvatarFallback className="bg-cyan-500 text-white text-lg">YOU</AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-cyan-500 text-white text-xs px-2 py-1">
                <Headphones className="w-3 h-3 mr-1" />
                Camera
              </Badge>
            </div>
            {/* Spatial Audio Waves */}
            <div className="absolute inset-0 animate-ping">
              <div className="w-full h-full rounded-full border-2 border-cyan-400 opacity-30"></div>
            </div>
          </div>
        </div>

        {/* 3D Floating Messages */}
        {spatialMessages.map((spatialMsg) => {
          const isVoiceMessage = spatialMsg.message.message_type === 'voice';
          const transform = calculateTransform(spatialMsg.position);

          return (
            <div
              key={spatialMsg.id}
              className={`absolute transition-all duration-500 ease-out cursor-pointer ${
                spatialMsg.isActive ? 'z-30' : 'z-10'
              }`}
              style={{
                left: '50%',
                top: '50%',
                transform: `translate(-50%, -50%) ${transform}`,
                filter: `brightness(${0.6 + spatialMsg.volume * 0.4})`,
                opacity: spatialMsg.volume * 0.8 + 0.2,
                transformStyle: 'preserve-3d'
              }}
              onClick={() => activateMessage(spatialMsg.id)}
            >
              <Card className={`w-64 backdrop-blur-md border-2 transition-all duration-300 ${
                spatialMsg.isActive 
                  ? 'bg-blue-500/30 border-blue-400 shadow-lg shadow-blue-500/50' 
                  : 'bg-white/10 border-white/20 hover:bg-white/20'
              }`}>
                <CardContent className="p-3">
                  {/* Message Header */}
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={spatialMsg.message.sender_profile?.avatar_url} />
                      <AvatarFallback className="text-xs">
                        {spatialMsg.message.sender_profile?.display_name?.slice(0, 2) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-white text-sm font-medium truncate">
                      {spatialMsg.message.sender_profile?.display_name || 'Unknown'}
                    </span>
                    {isVoiceMessage && (
                      <div className="flex items-center gap-1">
                        <Volume2 className="w-3 h-3 text-blue-400" />
                        <span className="text-xs text-blue-400">3D</span>
                      </div>
                    )}
                  </div>

                  {/* Message Content */}
                  {spatialMsg.message.message_type === 'text' && (
                    <p className="text-white/90 text-sm">
                      {spatialMsg.message.content}
                    </p>
                  )}

                  {isVoiceMessage && spatialMsg.message.voice_url && (
                    <div className="space-y-2">
                      {/* 3D Audio Visualizer */}
                      <div className="flex items-center justify-center gap-1 py-2">
                        {Array.from({ length: 8 }).map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-blue-400 rounded animate-pulse"
                            style={{
                              height: `${10 + Math.random() * 20}px`,
                              animationDelay: `${i * 0.1}s`,
                              opacity: volume
                            }}
                          />
                        ))}
                      </div>
                      
                      {/* Audio Player */}
                      <AudioPlayer 
                        src={spatialMsg.message.voice_url}
                        className="w-full"
                      />
                      
                      {/* Spatial Audio Info */}
                      <div className="flex items-center justify-between text-xs text-white/70">
                        <span>Volume: {Math.round(volume * 100)}%</span>
                        <span>Distance: {Math.round(calculateVolume(spatialMsg.position) * 100)}m</span>
                      </div>
                    </div>
                  )}

                  {/* Spatial Position Indicator */}
                  <div className="mt-2 flex items-center justify-between text-xs text-white/50">
                    <span>X:{Math.round(spatialMsg.position.x)} Y:{Math.round(spatialMsg.position.y)}</span>
                    <span>Z:{spatialMsg.position.z}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Floating Connection Lines */}
              {spatialMsg.isActive && (
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute top-1/2 left-1/2 w-32 h-32 border border-blue-400/50 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"></div>
                  <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-blue-400 rounded-full animate-pulse transform -translate-x-1/2 -translate-y-1/2"></div>
                </div>
              )}
            </div>
          );
        })}

        {/* 3D Controls Panel */}
        <div className="absolute top-4 right-4 z-40 space-y-2">
          <Button
            variant={isImmersiveMode ? "default" : "outline"}
            size="sm"
            onClick={() => setIsImmersiveMode(!isImmersiveMode)}
            className="bg-black/70 backdrop-blur-md border-cyan-400/30 text-white hover:bg-cyan-500/20"
          >
            <Maximize className="w-4 h-4 mr-2" />
            {isImmersiveMode ? 'Exit 3D' : '3D Mode'}
          </Button>

          <Button
            variant={spatialAudioEnabled ? "default" : "outline"}
            size="sm"
            onClick={() => setSpatialAudioEnabled(!spatialAudioEnabled)}
            className="bg-black/70 backdrop-blur-md border-cyan-400/30 text-white hover:bg-cyan-500/20"
          >
            {spatialAudioEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setCamera(prev => ({ ...prev, zoom: 1, rotation: { x: 0, y: 0, z: 0 } }))}
            className="bg-black/70 backdrop-blur-md border-cyan-400/30 text-white hover:bg-cyan-500/20"
          >
            <RotateCcw className="w-4 h-4" />
          </Button>

          <div className="flex flex-col gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCamera(prev => ({ ...prev, zoom: Math.min(5, prev.zoom * 1.2) }))}
              className="bg-black/70 backdrop-blur-md border-cyan-400/30 text-white hover:bg-cyan-500/20 h-8"
            >
              <ZoomIn className="w-3 h-3" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCamera(prev => ({ ...prev, zoom: Math.max(0.1, prev.zoom * 0.8) }))}
              className="bg-black/70 backdrop-blur-md border-cyan-400/30 text-white hover:bg-cyan-500/20 h-8"
            >
              <ZoomOut className="w-3 h-3" />
            </Button>
          </div>
        </div>

        {/* 3D Info Panel */}
        <div className="absolute bottom-4 left-4 z-40">
          <Card className="bg-black/70 backdrop-blur-md border-cyan-400/30">
            <CardContent className="p-3">
              <div className="text-white space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Move3D className="w-4 h-4 text-cyan-400" />
                  Real 3D Spatial Environment
                </div>
                <div className="text-xs text-white/70">
                  Camera: X:{Math.round(camera.position.x)} Y:{Math.round(camera.position.y)} Z:{Math.round(camera.position.z)}
                </div>
                <div className="text-xs text-white/70">
                  Rotation: X:{Math.round(camera.rotation.x)}° Y:{Math.round(camera.rotation.y)}° Z:{Math.round(camera.rotation.z)}°
                </div>
                <div className="text-xs text-white/70">
                  Zoom: {camera.zoom.toFixed(1)}x | Messages: {spatialMessages.length} | Active: {activeVoiceMessage ? '1' : '0'}
                </div>
                <div className="text-xs text-cyan-400">
                  Drag to rotate • Scroll to zoom • WASD/QE to move
                </div>
                <div className="text-xs text-cyan-400">
                  Click messages to activate • Press F for fullscreen
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default SpatialChatView;
