import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Globe, 
  Headphones, 
  Volume2, 
  VolumeX, 
  Mic, 
  MicOff,
  Users,
  Settings,
  Maximize,
  RotateCcw,
  Zap
} from 'lucide-react';
import { VoiceChatMessage } from '@/services/voiceChatService';
import AudioPlayer from '@/components/AudioPlayer';

interface SpatialChatViewProps {
  messages: VoiceChatMessage[];
  participants: any[];
  onSendMessage: (message: string) => void;
  onSendVoice: () => void;
  currentUserId: string;
}

interface SpatialPosition {
  x: number;
  y: number;
  z: number;
  rotation: number;
}

interface FloatingMessage {
  id: string;
  message: VoiceChatMessage;
  position: SpatialPosition;
  isActive: boolean;
}

const SpatialChatView: React.FC<SpatialChatViewProps> = ({
  messages,
  participants,
  onSendMessage,
  onSendVoice,
  currentUserId
}) => {
  const [spatialMessages, setSpatialMessages] = useState<FloatingMessage[]>([]);
  const [userPosition, setUserPosition] = useState<SpatialPosition>({ x: 50, y: 50, z: 0, rotation: 0 });
  const [isImmersiveMode, setIsImmersiveMode] = useState(false);
  const [spatialAudioEnabled, setSpatialAudioEnabled] = useState(true);
  const [activeVoiceMessage, setActiveVoiceMessage] = useState<string | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Generate spatial positions for messages
  useEffect(() => {
    const newSpatialMessages: FloatingMessage[] = messages.map((message, index) => {
      const angle = (index * 45) % 360; // Distribute around circle
      const radius = 30 + (index % 3) * 20; // Varying distances
      const x = 50 + Math.cos(angle * Math.PI / 180) * radius;
      const y = 50 + Math.sin(angle * Math.PI / 180) * radius;
      const z = (index % 5) * 10; // Depth layers
      
      return {
        id: message.id,
        message,
        position: { x, y, z, rotation: angle },
        isActive: false
      };
    });
    
    setSpatialMessages(newSpatialMessages);
  }, [messages]);

  // Calculate distance-based volume
  const calculateVolume = (messagePosition: SpatialPosition): number => {
    const dx = userPosition.x - messagePosition.x;
    const dy = userPosition.y - messagePosition.y;
    const dz = userPosition.z - messagePosition.z;
    const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
    return Math.max(0.1, 1 - distance / 100);
  };

  // Handle user movement in 3D space
  const handleSpaceClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;
    
    setUserPosition(prev => ({ ...prev, x, y }));
  };

  const activateMessage = (messageId: string) => {
    setSpatialMessages(prev => 
      prev.map(msg => ({
        ...msg,
        isActive: msg.id === messageId
      }))
    );
    setActiveVoiceMessage(messageId);
  };

  return (
    <div className={`relative w-full h-full ${isImmersiveMode ? 'fixed inset-0 z-50 bg-black' : 'h-96'}`}>
      {/* 3D Spatial Environment */}
      <div 
        ref={containerRef}
        className="relative w-full h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 overflow-hidden cursor-crosshair"
        onClick={handleSpaceClick}
        style={{
          perspective: '1000px',
          transformStyle: 'preserve-3d'
        }}
      >
        {/* Floating Grid Background */}
        <div className="absolute inset-0 opacity-20">
          <div className="grid grid-cols-10 grid-rows-10 w-full h-full">
            {Array.from({ length: 100 }).map((_, i) => (
              <div 
                key={i} 
                className="border border-blue-400/30 animate-pulse"
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
        </div>

        {/* Floating Particles */}
        <div className="absolute inset-0">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-blue-400 rounded-full animate-float"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 5}s`,
                animationDuration: `${3 + Math.random() * 4}s`
              }}
            />
          ))}
        </div>

        {/* User Avatar (Current Position) */}
        <div
          className="absolute z-20 transition-all duration-500 ease-out"
          style={{
            left: `${userPosition.x}%`,
            top: `${userPosition.y}%`,
            transform: `translate(-50%, -50%) translateZ(${userPosition.z}px) rotateY(${userPosition.rotation}deg)`
          }}
        >
          <div className="relative">
            <Avatar className="w-12 h-12 ring-4 ring-blue-400 ring-opacity-60 shadow-lg shadow-blue-500/50">
              <AvatarFallback className="bg-blue-500 text-white">YOU</AvatarFallback>
            </Avatar>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-blue-500 text-white text-xs px-2 py-1">
                <Headphones className="w-3 h-3 mr-1" />
                You
              </Badge>
            </div>
            {/* Spatial Audio Waves */}
            <div className="absolute inset-0 animate-ping">
              <div className="w-full h-full rounded-full border-2 border-blue-400 opacity-30"></div>
            </div>
          </div>
        </div>

        {/* Floating Messages in 3D Space */}
        {spatialMessages.map((spatialMsg) => {
          const volume = calculateVolume(spatialMsg.position);
          const isVoiceMessage = spatialMsg.message.message_type === 'voice';
          
          return (
            <div
              key={spatialMsg.id}
              className={`absolute transition-all duration-700 ease-out cursor-pointer ${
                spatialMsg.isActive ? 'z-30 scale-110' : 'z-10'
              }`}
              style={{
                left: `${spatialMsg.position.x}%`,
                top: `${spatialMsg.position.y}%`,
                transform: `translate(-50%, -50%) translateZ(${spatialMsg.position.z}px) rotateY(${spatialMsg.position.rotation}deg)`,
                filter: `brightness(${0.7 + volume * 0.3})`,
              }}
              onClick={() => activateMessage(spatialMsg.id)}
            >
              <Card className={`w-64 backdrop-blur-md border-2 transition-all duration-300 ${
                spatialMsg.isActive 
                  ? 'bg-blue-500/30 border-blue-400 shadow-lg shadow-blue-500/50' 
                  : 'bg-white/10 border-white/20 hover:bg-white/20'
              }`}>
                <CardContent className="p-3">
                  {/* Message Header */}
                  <div className="flex items-center gap-2 mb-2">
                    <Avatar className="w-6 h-6">
                      <AvatarImage src={spatialMsg.message.sender_profile?.avatar_url} />
                      <AvatarFallback className="text-xs">
                        {spatialMsg.message.sender_profile?.display_name?.slice(0, 2) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-white text-sm font-medium truncate">
                      {spatialMsg.message.sender_profile?.display_name || 'Unknown'}
                    </span>
                    {isVoiceMessage && (
                      <div className="flex items-center gap-1">
                        <Volume2 className="w-3 h-3 text-blue-400" />
                        <span className="text-xs text-blue-400">3D</span>
                      </div>
                    )}
                  </div>

                  {/* Message Content */}
                  {spatialMsg.message.message_type === 'text' && (
                    <p className="text-white/90 text-sm">
                      {spatialMsg.message.content}
                    </p>
                  )}

                  {isVoiceMessage && spatialMsg.message.voice_url && (
                    <div className="space-y-2">
                      {/* 3D Audio Visualizer */}
                      <div className="flex items-center justify-center gap-1 py-2">
                        {Array.from({ length: 8 }).map((_, i) => (
                          <div
                            key={i}
                            className="w-1 bg-blue-400 rounded animate-pulse"
                            style={{
                              height: `${10 + Math.random() * 20}px`,
                              animationDelay: `${i * 0.1}s`,
                              opacity: volume
                            }}
                          />
                        ))}
                      </div>
                      
                      {/* Audio Player */}
                      <AudioPlayer 
                        src={spatialMsg.message.voice_url}
                        className="w-full"
                      />
                      
                      {/* Spatial Audio Info */}
                      <div className="flex items-center justify-between text-xs text-white/70">
                        <span>Volume: {Math.round(volume * 100)}%</span>
                        <span>Distance: {Math.round(calculateVolume(spatialMsg.position) * 100)}m</span>
                      </div>
                    </div>
                  )}

                  {/* Spatial Position Indicator */}
                  <div className="mt-2 flex items-center justify-between text-xs text-white/50">
                    <span>X:{Math.round(spatialMsg.position.x)} Y:{Math.round(spatialMsg.position.y)}</span>
                    <span>Z:{spatialMsg.position.z}</span>
                  </div>
                </CardContent>
              </Card>

              {/* Floating Connection Lines */}
              {spatialMsg.isActive && (
                <div className="absolute inset-0 pointer-events-none">
                  <div className="absolute top-1/2 left-1/2 w-32 h-32 border border-blue-400/50 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2"></div>
                  <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-blue-400 rounded-full animate-pulse transform -translate-x-1/2 -translate-y-1/2"></div>
                </div>
              )}
            </div>
          );
        })}

        {/* Spatial Controls */}
        <div className="absolute top-4 right-4 z-40 space-y-2">
          <Button
            variant={isImmersiveMode ? "default" : "outline"}
            size="sm"
            onClick={() => setIsImmersiveMode(!isImmersiveMode)}
            className="bg-black/50 backdrop-blur-md border-white/20 text-white hover:bg-white/20"
          >
            <Maximize className="w-4 h-4 mr-2" />
            {isImmersiveMode ? 'Exit' : 'Immersive'}
          </Button>
          
          <Button
            variant={spatialAudioEnabled ? "default" : "outline"}
            size="sm"
            onClick={() => setSpatialAudioEnabled(!spatialAudioEnabled)}
            className="bg-black/50 backdrop-blur-md border-white/20 text-white hover:bg-white/20"
          >
            {spatialAudioEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
          </Button>
        </div>

        {/* Spatial Info Panel */}
        <div className="absolute bottom-4 left-4 z-40">
          <Card className="bg-black/50 backdrop-blur-md border-white/20">
            <CardContent className="p-3">
              <div className="text-white space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Globe className="w-4 h-4 text-blue-400" />
                  3D Spatial Audio Environment
                </div>
                <div className="text-xs text-white/70">
                  Position: X:{Math.round(userPosition.x)} Y:{Math.round(userPosition.y)} Z:{userPosition.z}
                </div>
                <div className="text-xs text-white/70">
                  Messages: {spatialMessages.length} | Active: {activeVoiceMessage ? '1' : '0'}
                </div>
                <div className="text-xs text-blue-400">
                  Click anywhere to move • Click messages to activate
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default SpatialChatView;
